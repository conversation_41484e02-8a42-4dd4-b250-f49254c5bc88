diff --git a/Dockerfile b/Dockerfile
index 86a7190..298472e 100644
--- a/Dockerfile
+++ b/Dockerfile
@@ -3,7 +3,7 @@ WORKDIR /app
 COPY . .
 RUN ./mvnw clean package -DskipTests
 
-FROM quay.io/keycloak/keycloak:23.0 AS optimizer
+FROM quay.io/keycloak/keycloak:26.0 AS optimizer
 
 COPY --from=builder /app/target/keycloak-bodhi-ext.jar /opt/keycloak/providers/keycloak-bodhi-ext.jar
 COPY cache-ispn-jdbc-ping.xml /opt/keycloak/conf/cache-ispn-jdbc-ping.xml
@@ -17,7 +17,7 @@ RUN /opt/keycloak/bin/kc.sh build \
     --http-relative-path=/ \
     --health-enabled=true
 
-FROM quay.io/keycloak/keycloak:23.0
+FROM quay.io/keycloak/keycloak:26.0
 
 COPY --from=optimizer /opt/keycloak/ /opt/keycloak/
 ENTRYPOINT ["/opt/keycloak/bin/kc.sh"]
diff --git a/Makefile b/Makefile
index 1d427b8..46794ad 100644
--- a/Makefile
+++ b/Makefile
@@ -1,7 +1,7 @@
 .PHONY: setup
 
 setup:
-	java -jar tools/keycloak-config-cli-23.0.7.jar \
+	java -jar tools/keycloak-config-cli-26.0.8.jar \
 		--import.files.locations=realm-setup.json \
 		--keycloak.url=${DEV_KC_URL} \
 		--keycloak.user=${DEV_KC_ADMIN} \
diff --git a/pom.xml b/pom.xml
index a521c65..5efd233 100644
--- a/pom.xml
+++ b/pom.xml
@@ -9,13 +9,13 @@
   <name>keycloak-bodhi-ext</name>
   <url>http://maven.apache.org</url>
   <properties>
-    <keycloak.version>23.0.7</keycloak.version>
+    <keycloak.version>26.0.8</keycloak.version>
     <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
     <maven.compiler.source>17</maven.compiler.source>
     <maven.compiler.target>17</maven.compiler.target>
   </properties>
   <dependencies>
-    <dependency>
+    <dependency> 
       <groupId>org.keycloak</groupId>
       <artifactId>keycloak-server-spi</artifactId>
       <version>${keycloak.version}</version>
@@ -40,7 +40,7 @@
       <artifactId>jakarta.json</artifactId>
       <version>2.0.1</version>
     </dependency>
-    <dependency>
+    <!-- <dependency>
       <groupId>org.slf4j</groupId>
       <artifactId>slf4j-api</artifactId>
       <version>2.0.13</version>
@@ -49,7 +49,7 @@
       <groupId>ch.qos.logback</groupId>
       <artifactId>logback-classic</artifactId>
       <version>1.5.6</version>
-    </dependency>
+    </dependency> -->
     <dependency>
       <groupId>com.fasterxml.jackson.core</groupId>
       <artifactId>jackson-databind</artifactId>
@@ -69,13 +69,13 @@
     <dependency>
       <groupId>com.github.dasniko</groupId>
       <artifactId>testcontainers-keycloak</artifactId>
-      <version>3.2.0</version>
+      <version>3.5.1</version>
       <scope>test</scope>
     </dependency>
     <dependency>
       <groupId>org.testcontainers</groupId>
       <artifactId>junit-jupiter</artifactId>
-      <version>1.19.3</version>
+      <version>1.20.2</version>
       <scope>test</scope>
     </dependency>
     <dependency>
@@ -116,6 +116,11 @@
         <groupId>org.apache.maven.plugins</groupId>
         <artifactId>maven-surefire-plugin</artifactId>
         <version>3.2.5</version>
+        <configuration>
+          <systemPropertyVariables>
+            <java.util.logging.manager>org.jboss.logmanager.LogManager</java.util.logging.manager>
+          </systemPropertyVariables>
+        </configuration>
       </plugin>
     </plugins>
   </build>
diff --git a/src/main/java/com/bodhisearch/BodhiResourceProvider.java b/src/main/java/com/bodhisearch/BodhiResourceProvider.java
index 5e2434a..d88926f 100644
--- a/src/main/java/com/bodhisearch/BodhiResourceProvider.java
+++ b/src/main/java/com/bodhisearch/BodhiResourceProvider.java
@@ -168,7 +168,7 @@ public class BodhiResourceProvider implements RealmResourceProvider {
     AuthorizationProvider authorization = session.getProvider(AuthorizationProvider.class);
     PolicyStore policyStore = authorization.getStoreFactory().getPolicyStore();
     for (Policy policy : policies) {
-      policyStore.delete(realm, policy.getId());
+      policyStore.delete(policy.getId());
     }
     ClientModel realmManagement = realm.getClientByClientId("realm-management");
     ResourceServer resourceServer = authorization.getStoreFactory().getResourceServerStore()
diff --git a/src/test/java/com/bodhisearch/BaseTest.java b/src/test/java/com/bodhisearch/BaseTest.java
index c550ca3..a3ef7cb 100644
--- a/src/test/java/com/bodhisearch/BaseTest.java
+++ b/src/test/java/com/bodhisearch/BaseTest.java
@@ -62,7 +62,7 @@ public class BaseTest {
   protected static RealmResource realm;
 
   @Container
-  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
+  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.0.8")
       .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
       .withProviderClassesFrom("target/classes")
       .withEnv("APP_ENV", "test")
@@ -93,7 +93,7 @@ public class BaseTest {
     try {
       String[] command = { "java", "-jar",
           // "tools/keycloak-config-cli-24.0.5.jar",
-          "tools/keycloak-config-cli-23.0.7.jar",
+          "tools/keycloak-config-cli-26.0.5.jar",
           "--import.files.locations=" + filename,
           "--keycloak.url=" + keycloakUrl,
           "--keycloak.user=" + username,
diff --git a/src/test/java/com/bodhisearch/CliDebugTest.java b/src/test/java/com/bodhisearch/CliDebugTest.java
index 5d7eda4..338967f 100644
--- a/src/test/java/com/bodhisearch/CliDebugTest.java
+++ b/src/test/java/com/bodhisearch/CliDebugTest.java
@@ -18,7 +18,7 @@ public class CliDebugTest {
   public static final Logger LOGGER = LoggerFactory.getLogger(CliDebugTest.class);
 
   @Container
-  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
+  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.8")
       .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
       .withProviderClassesFrom("target/classes")
   // .withDebugFixedPort(8787, true)
@@ -35,7 +35,7 @@ public class CliDebugTest {
     try {
       String[] command = { "java", "-jar",
           // "tools/keycloak-config-cli-24.0.5.jar",
-          "tools/keycloak-config-cli-23.0.7.jar",
+          "tools/keycloak-config-cli-23.0.8.jar",
           "--import.files.locations=" + filename,
           "--keycloak.url=" + keycloakUrl,
           "--keycloak.user=" + username,
diff --git a/src/test/java/com/bodhisearch/RegisterResourceTest.java b/src/test/java/com/bodhisearch/RegisterResourceTest.java
index 1c62dc8..210c96f 100644
--- a/src/test/java/com/bodhisearch/RegisterResourceTest.java
+++ b/src/test/java/com/bodhisearch/RegisterResourceTest.java
@@ -101,7 +101,8 @@ public class RegisterResourceTest extends BaseTest {
     assertThat(resourceToken, notNullValue());
 
     // token exchange checks
-    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
+    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass",
+        Arrays.asList("openid", "email", "profile"));
     String exchangeToken = exchangeTokenWith(CLIENT_LMNO, userToken, clientId,
         String.format("Bearer %s", resourceToken));
     assertNotNull(exchangeToken);
diff --git a/tools/keycloak-config-cli-26.0.5.jar b/tools/keycloak-config-cli-26.0.5.jar
new file mode 100644
index 0000000..0029f5c
Binary files /dev/null and b/tools/keycloak-config-cli-26.0.5.jar differ
