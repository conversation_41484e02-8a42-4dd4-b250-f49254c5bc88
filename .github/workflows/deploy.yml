# name: Deploy to Railway

# on:
#   workflow_run:
#     workflows: ["Build and Push Docker Image"]
#     types:
#       - completed

# permissions:
#   contents: read
#   packages: read

# jobs:
#   deploy:
#     runs-on: ubuntu-latest
#     if: ${{ github.event.workflow_run.conclusion == 'success' }}

#     steps:
#       - uses: actions/checkout@v4
#       - name: Install Railway CLI
#         run: npm install -g @railway/cli
#       - name: Deploy to Railway
#         env:
#           RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
#           RAILWAY_UP_IMAGE: ghcr.io/${{ github.repository }}/keycloak-bodhi-ext:sha-${{ github.event.workflow_run.head_sha }}
#         run: |
#           railway link --project-id ${{ secrets.RAILWAY_PROJECT_ID }} --service ${{ secrets.RAILWAY_SERVICE_ID }}
#           railway up
