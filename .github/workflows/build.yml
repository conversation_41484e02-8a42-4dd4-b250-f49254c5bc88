name: Build and Push Docker Image

on:
  push:
    branches: [main]

permissions:
  contents: read
  packages: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "temurin"
          cache: "maven"

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with Maven
        run: mvn clean test package

      # - name: Set up Docker Buildx
      #   uses: docker/setup-buildx-action@v3

      # - name: Login to GitHub Container Registry
      #   uses: docker/login-action@v3
      #   with:
      #     registry: ghcr.io
      #     username: ${{ github.actor }}
      #     password: ${{ secrets.GITHUB_TOKEN }}

      # - name: Prepare Docker metadata
      #   id: meta
      #   uses: docker/metadata-action@v5
      #   with:
      #     images: |
      #       ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}
      #     tags: |
      #       type=sha

      # - name: Build and push
      #   uses: docker/build-push-action@v6
      #   with:
      #     context: .
      #     push: true
      #     tags: ${{ steps.meta.outputs.tags }}
      #     labels: ${{ steps.meta.outputs.labels }}

      # - name: Install Railway CLI
      #   run: npm install -g @railway/cli
      # - name: Deploy to Railway
      #   env:
      #     RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      #   run: |
      #     railway link --project-id ${{ secrets.RAILWAY_PROJECT_ID }} --service ${{ secrets.RAILWAY_SERVICE_ID }}
      #     railway up
