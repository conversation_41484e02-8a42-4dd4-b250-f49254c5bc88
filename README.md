# Setting up Keycloak for Bodhi Server - Checklist

- Create a new realm `bodhi`
- In the new realm, make every client scope Optional. These include - acr, profile, role_list, roles, web-origins
- there is no need to create client global scopes, as our scope are at client level  
- Clients > realm-management > Authorization > Policies > Create Client Policy
 - Select audience match policy
 - Give name audience-match-policy
 - Save
- in Realm Settings > Login
 - Email as username
 - Login with email
 - Verify email
- Realm Settings > Email
 - Provide email template for Verify email
 - Setup SMTP provider
- Realm Setting > Session
 - Increase sso idle time to 7 days
 - Increase sso max time, sso idle remember me, sso session max remember me to 30 days