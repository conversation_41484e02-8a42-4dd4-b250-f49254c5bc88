# Keycloak Upgrade Analysis: 23.0.7 → 26.2.5

## Current Status: 10% Complete
- ❌ Tests failing with PolicyStore null pointer exceptions
- ❌ Authorization services configuration incomplete
- ❌ keycloak-config-cli compatibility issues
- ✅ Basic infrastructure (Docker, Java 24, <PERSON>ven compilation)

## Problem Statement
The Bodhi extension tests are failing because the realm configuration is incomplete. The extension expects specific authorization policies and configurations that are not being created properly.

## Investigation Plan
1. **API Changes Analysis**: Research Keycloak API changes from 23→24→25→26
2. **Authorization Services Changes**: Understand what changed in authorization model
3. **PolicyStore Changes**: Analyze the specific PolicyStore API changes
4. **Configuration Format Changes**: Check if realm JSON format changed
5. **CLI Tool Compatibility**: Find compatible keycloak-config-cli version

## Step 1: API Changes Research
Starting systematic research of Keycloak version changes...

### Version 23 → 24 Changes
From Keycloak upgrade guide:
- No specific authorization services breaking changes mentioned
- Need to investigate PolicyStore interface changes
- Admin client API changes

### Version 24 → 25 Changes
- TBD

### Version 25 → 26 Changes
- TBD

## Step 2: Specific Error Investigation
The error occurs in BodhiResourceProvider.java where it tries to delete policies:

```java
List<Policy> policies = new ArrayList<>() {
  {
    add(clientMgmt.managePermission(client));
    add(clientMgmt.configurePermission(client));
    add(clientMgmt.viewPermission(client));
    add(clientMgmt.mapRolesCompositePermission(client));
    add(clientMgmt.mapRolesClientScopePermission(client));
    add(clientMgmt.mapRolesPermission(client));
  }
};
for (Policy policy : policies) {
  policyStore.delete(policy.getId());  // policy is null here
}
```

The issue is that one or more of the permission methods are returning null instead of Policy objects.

## Step 3: Understanding ClientPermissionManagement API
From the Keycloak 26.2.5 javadoc, the ClientPermissionManagement interface has these methods:
- `Policy managePermission(ClientModel client)`
- `Policy configurePermission(ClientModel client)`
- `Policy viewPermission(ClientModel client)`
- `Policy mapRolesCompositePermission(ClientModel client)`
- `Policy mapRolesClientScopePermission(ClientModel client)`
- `Policy mapRolesPermission(ClientModel client)`

All these methods should return Policy objects, but they're returning null, causing the NPE.

## Step 4: Root Cause Analysis
The problem is likely that:
1. Authorization services are not properly enabled for the realm-management client
2. The policies are not being created during realm setup
3. There's a version compatibility issue with how authorization services are configured

The realm-management client needs to have authorization services enabled and the appropriate policies created for the ClientPermissionManagement methods to work.

## Step 5: Found the Root Cause!
Looking at the FTL template `src/test/resources/import-files/bodhi-realm-setup.ftl`, the realm-management client configuration is incomplete:

```json
{
  "clientId": "realm-management",
  "name": "${client_realm-management}",
  "enabled": true
}
```

This is missing:
1. `"authorizationServicesEnabled": true`
2. Authorization services configuration (resources, policies, scopes)

The realm-management client needs authorization services enabled with the proper policies for the ClientPermissionManagement API to work.

## Step 6: Applied Fix
Updated the FTL template to include:
1. `"authorizationServicesEnabled": true`
2. `"serviceAccountsEnabled": true`
3. Complete `authorizationSettings` with:
   - Resources with proper scopes (view, manage, configure, map-roles, etc.)
   - Default policy granting access to realm-admin role
   - All required scopes defined

This should resolve the null policy issue by ensuring the authorization services are properly configured when the realm is imported.

## Step 7: Test Execution Issue
The `testDeleteResource` method doesn't exist in BodhiResourceProviderTest.java. Available test methods:
- testMakeFirstAdmin
- testMakeFirstAdminUnauthorized
- testMakeFirstAdminErrorIfNotServiceToken
- testMakeFirstAdminInvalidToken
- testMakeFirstAdminFailsIfAlreadyHaveAdmin
- testMakeFirstAdminFailsIfTopLevelGroupNotConfigured
- testMakeFirstAdminUserNotFound
- testAddUserToGroupByResourceAdmin
- testAddUserToGroupAlreadyMember
- testAddUserToGroupUnauthorized
- testAddUserToGroupByOtherClientAdmin
- testHasAdminOnEmptyClient
- testHasAdminOnClientWithAdmin
- testHasAdminReturnsUnauthorizedIfCalledWithNonServiceToken

Let me run a simple test to see if the authorization services configuration is working.

## Current Error Analysis
```
Cannot invoke "org.keycloak.authorization.model.Policy.getId()" because "policy" is null
```

This occurs in BodhiResourceProvider.java line 186 where:
```java
for (Policy policy : policies) {
  policyStore.delete(policy.getId());  // policy is null here
}
```

The policies list contains null values, indicating that the authorization services setup is incomplete.

## Next Actions
1. Research Keycloak authorization API changes
2. Analyze what policies should exist
3. Fix realm configuration
4. Test and iterate
