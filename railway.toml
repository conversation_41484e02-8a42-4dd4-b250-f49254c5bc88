[build]
builder = "NIXPACKS"

[deploy]
startCommand = "/opt/keycloak/bin/kc.sh start"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments]
dev.name = "dev"

[[plugins]]
name = "postgresql"

[plugins.postgresql]
version = "14"

[environments.dev.plugins.postgresql]
enabled = true

[envs]
KC_HTTP_PORT = "${PORT}"
KC_DB_URL = "${PGDATABASE_URL}"
KC_DB_USERNAME = "${PGUSER}"
KC_DB_PASSWORD = "${PGPASSWORD}"
KC_DB = "postgres"
KC_HEALTH_ENABLED = "true"
KC_HOSTNAME_STRICT = "false"
KC_HOSTNAME_STRICT_HTTPS = "false"
KC_PROXY = "edge"
KC_LOG_LEVEL = "INFO"
KC_CACHE = "ispn"
# KC_CACHE_CONFIG_FILE = "cache-ispn-jdbc-ping.xml"
KC_DB_POOL_INITIAL_SIZE = "5"
KC_DB_POOL_MIN_SIZE = "5"
KC_DB_POOL_MAX_SIZE = "15"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_FAILURES = "5"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_WAIT_INCREMENT = "60"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_WAIT = "300"
# KC_FEATURES = "admin-fine-grained-authz,token-exchange,persistent-user-sessions" # available in > v25.0
KC_FEATURES = "admin-fine-grained-authz,token-exchange"
JAVA_OPTS = "-Xms2g -Xmx4g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC"
QUARKUS_TRANSACTION_MANAGER_ENABLE_RECOVERY = "true"
KC_PROXY_HEADERS = "forwarded"
# JGROUPS_DISCOVERY_PROTOCOL = "JDBC_PING"
# JGROUPS_DISCOVERY_PROPERTIES = "datasource_jndi_name=java:jboss/datasources/KeycloakDS,info_writer_sleep_time=500"
