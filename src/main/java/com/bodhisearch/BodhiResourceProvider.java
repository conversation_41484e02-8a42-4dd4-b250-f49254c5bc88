package com.bodhisearch;

import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.keycloak.authorization.AuthorizationProvider;
import org.keycloak.authorization.model.Policy;
import org.keycloak.authorization.model.ResourceServer;
import org.keycloak.authorization.store.PolicyStore;
import org.keycloak.crypto.Algorithm;
import org.keycloak.crypto.KeyUse;
import org.keycloak.crypto.KeyWrapper;
import org.keycloak.models.ClientModel;
import org.keycloak.models.GroupModel;
import org.keycloak.models.GroupProvider;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.RoleModel;
import org.keycloak.models.UserModel;
import org.keycloak.models.utils.KeycloakModelUtils;
import org.keycloak.services.managers.AppAuthManager;
import org.keycloak.services.managers.AuthenticationManager;
import org.keycloak.services.managers.AuthenticationManager.AuthResult;
import org.keycloak.services.resource.RealmResourceProvider;
import org.keycloak.services.resources.admin.permissions.AdminPermissionManagement;
import org.keycloak.services.resources.admin.permissions.AdminPermissions;
import org.keycloak.services.resources.admin.permissions.ClientPermissionManagement;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BodhiResourceProvider implements RealmResourceProvider {
  private static final Logger logger = LoggerFactory.getLogger(BodhiResourceProvider.class);
  private static final String RESOURCE_USER = "resource_user";
  private static final String RESOURCE_POWER_USER = "resource_power_user";
  private static final String RESOURCE_MANAGER = "resource_manager";
  private static final String RESOURCE_ADMIN = "resource_admin";
  private static final String GROUP_USERS_TEMPLATE = "users-%s";
  private static final String GROUP_MANAGERS = "managers";
  private static final String GROUP_ADMINS = "admins";
  private static final String GROUP_USERS = "users";
  private static final String GROUP_POWER_USERS = "power-users";
  private KeycloakSession session;

  public BodhiResourceProvider(KeycloakSession session) {
    this.session = session;
  }

  @Override
  public void close() {
  }

  @Override
  public Object getResource() {
    return this;
  }

  @POST
  @Path("clients")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response newResource(ResourceRequest request) {
    try {
      return newResourceInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response newResourceInternal(ResourceRequest request) {
    String appEnv = System.getenv("APP_ENV");
    String resourcePrefix = "resource-";
    boolean directAccessGrantsEnabled = false;
    if ("test".equals(appEnv)) { // enable direct access grants in testing
      directAccessGrantsEnabled = true;
    }
    if ("dev".equals(appEnv) || "test".equals(appEnv)) {
      boolean liveTest = session.getContext().getUri().getQueryParameters().containsKey("live_test");
      if (liveTest) { // prefix resources with test that are created by live test on dev server
        resourcePrefix = "test-resource-";
        directAccessGrantsEnabled = true;
      }
    }

    RealmModel realm = session.getContext().getRealm();

    Map<String, String> responseBody = new HashMap<>();
    KeyWrapper key = session.keys().getActiveKey(realm, KeyUse.SIG, Algorithm.RS256);
    byte[] publicKey = key.getPublicKey().getEncoded();
    if (publicKey != null) {
      String publicKeyBase64 = Base64.getEncoder().encodeToString(publicKey);
      responseBody.put("public_key", publicKeyBase64);
      responseBody.put("kid", key.getKid());
      responseBody.put("alg", key.getAlgorithm());
    } else {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse("not able to get signing key info, try later"))
          .build();
    }
    String issuerUrl = session.getContext().getUri().getBaseUri().toString() + "realms/" + realm.getName();
    responseBody.put("issuer", issuerUrl);

    // Create a new client
    String clientId = String.format("%s%s", resourcePrefix, KeycloakModelUtils.generateId());
    ClientModel client = realm.addClient(clientId);
    client.setEnabled(true);
    for (String redirectUri : request.redirectUris) {
      client.addRedirectUri(redirectUri);
    }
    client.addWebOrigin("+");
    client.setClientAuthenticatorType("client-secret");
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);

    // for testing is true, for production is false
    client.setDirectAccessGrantsEnabled(directAccessGrantsEnabled);

    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setFullScopeAllowed(false);
    client.setProtocol("openid-connect");

    String clientSecret = KeycloakModelUtils.generateSecret(client);
    client.setSecret(clientSecret);

    client.addRole(RESOURCE_USER);
    client.addRole(RESOURCE_POWER_USER);
    client.addRole(RESOURCE_MANAGER);
    client.addRole(RESOURCE_ADMIN);

    // Create group structure
    GroupModel topLevelGroup = realm.createGroup(String.format("users-%s", client.getClientId()));

    GroupModel usersGroup = realm.createGroup(null, GROUP_USERS, topLevelGroup);
    GroupModel powerUsersGroup = realm.createGroup(null, GROUP_POWER_USERS, topLevelGroup);
    GroupModel managersGroup = realm.createGroup(null, GROUP_MANAGERS, topLevelGroup);
    GroupModel adminsGroup = realm.createGroup(null, GROUP_ADMINS, topLevelGroup);

    // Assign roles to groups
    usersGroup.grantRole(client.getRole(RESOURCE_USER));
    powerUsersGroup.grantRole(client.getRole(RESOURCE_USER));
    powerUsersGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_MANAGER));
    adminsGroup.grantRole(client.getRole(RESOURCE_USER));
    adminsGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    adminsGroup.grantRole(client.getRole(RESOURCE_MANAGER));
    adminsGroup.grantRole(client.getRole(RESOURCE_ADMIN));

    // Create token exchange policy
    AdminPermissionManagement permissions = AdminPermissions.management(session, realm);
    ClientPermissionManagement clientMgmt = permissions.clients();
    clientMgmt.setPermissionsEnabled(client, true);

    List<Policy> policies = new ArrayList<>() {
      {
        add(clientMgmt.managePermission(client));
        add(clientMgmt.configurePermission(client));
        add(clientMgmt.viewPermission(client));
        add(clientMgmt.mapRolesCompositePermission(client));
        add(clientMgmt.mapRolesClientScopePermission(client));
        add(clientMgmt.mapRolesPermission(client));
      }
    };
    AuthorizationProvider authorization = session.getProvider(AuthorizationProvider.class);
    PolicyStore policyStore = authorization.getStoreFactory().getPolicyStore();
    for (Policy policy : policies) {
      policyStore.delete(policy.getId());
    }
    ClientModel realmManagement = realm.getClientByClientId("realm-management");
    ResourceServer resourceServer = authorization.getStoreFactory().getResourceServerStore()
        .findByClient(realmManagement);
    Policy allowExchangePolicy = policyStore.findByName(resourceServer, "audience-match-policy");
    Policy policy = clientMgmt.exchangeToPermission(client);
    policy.addAssociatedPolicy(allowExchangePolicy);

    responseBody.put("client_id", client.getClientId());
    responseBody.put("client_secret", clientSecret);
    return Response.status(Response.Status.CREATED).entity(responseBody).build();
  }

  @POST
  @Path("clients/make-resource-admin")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response makeFirstResourceAdmin(MakeAdminRequest request) {
    try {
      return makeFirstResourceAdminInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response makeFirstResourceAdminInternal(MakeAdminRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForServiceAccount(authResult);
      boolean hasAdmin = hasAdmin(authResult);
      if (hasAdmin) {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(new ErrorResponse("resource already has a admin user"))
            .build();
      }
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    String resourceGroupName = String.format(GROUP_USERS_TEMPLATE, clientId);
    Optional<GroupModel> topLevelGroup = session.getProvider(GroupProvider.class).getTopLevelGroupsStream(realm)
        .filter(g -> g.getName().equals(resourceGroupName)).findFirst();
    if (topLevelGroup.isEmpty()) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse("resource group not configured for client")).build();
    }
    Optional<GroupModel> adminGroup = topLevelGroup.get().getSubGroupsStream(GROUP_ADMINS, true, 0, 1).findFirst();
    if (adminGroup.isEmpty()) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse("admin group not configured for client")).build();
    }
    UserModel user = session.users().getUserByUsername(realm, request.username);
    if (user == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("user not found")).build();
    }
    if (user.isMemberOf(adminGroup.get())) {
      return Response.ok().entity(new SuccessResponse("user already member of admin group")).build();
    }
    user.joinGroup(adminGroup.get());
    return Response.status(Response.Status.CREATED).build();
  }

  @GET
  @Path("clients/has-resource-admin")
  @Produces(MediaType.APPLICATION_JSON)
  public Response hasResourceAdmin() {
    try {
      return hasResourceAdminInternal();
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response hasResourceAdminInternal() {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForServiceAccount(authResult);
      boolean hasAdmin = hasAdmin(authResult);
      return Response.status(200).entity(new HasAdminResponse(hasAdmin)).build();
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
  }

  private boolean hasAdmin(AuthResult authResult) throws ResourceProviderException {
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    String resourceGroupName = String.format(GROUP_USERS_TEMPLATE, clientId);
    Optional<GroupModel> topLevelGroup = session.getProvider(GroupProvider.class).getTopLevelGroupsStream(realm)
        .filter(g -> g.getName().equals(resourceGroupName)).findFirst();
    if (topLevelGroup.isEmpty()) {
      throw new ResourceProviderException("resource group not configured for client",
          Response.Status.INTERNAL_SERVER_ERROR);
    }
    Optional<GroupModel> adminGroup = topLevelGroup.get().getSubGroupsStream(GROUP_ADMINS, true, 0, 1).findFirst();
    if (adminGroup.isEmpty()) {
      throw new ResourceProviderException("admin group not configured for client",
          Response.Status.INTERNAL_SERVER_ERROR);
    }
    return session.users().getGroupMembersStream(realm, adminGroup.get()).findFirst().isPresent();
  }

  @POST
  @Path("clients/add-user-to-group")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response addUserToGroup(AddToGroupRequest request) {
    try {
      return addUserToGroupInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response addUserToGroupInternal(AddToGroupRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    if (authResult == null) {
      return Response.status(Response.Status.UNAUTHORIZED).entity(new ErrorResponse("invalid session")).build();
    }
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    ClientModel client = realm.getClientByClientId(clientId);
    if (client == null) {
      return Response.status(Response.Status.NOT_FOUND).entity(new ErrorResponse("Client not found")).build();
    }
    RoleModel adminRole = client.getRole(RESOURCE_ADMIN);
    if (adminRole == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("resource-admin role not found"))
          .build();
    }
    UserModel adminUser = authResult.getUser();
    if (!adminUser.hasRole(adminRole)) {
      return Response.status(Response.Status.FORBIDDEN)
          .entity(new ErrorResponse("User does not have resource-admin role"))
          .build();
    }
    UserModel user = session.users().getUserByUsername(realm, request.username);
    if (user == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("User to approve not found"))
          .build();
    }
    String groupName = request.group;
    if (groupName == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("invalid role"))
          .build();
    }
    String clientGroup = String.format(GROUP_USERS_TEMPLATE, clientId);
    Optional<GroupModel> topLevelGroup = session.getProvider(GroupProvider.class).getTopLevelGroupsStream(realm)
        .filter(g -> g.getName().equals(clientGroup)).findFirst();
    if (topLevelGroup.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse(String.format("Client does not have %s group", clientGroup)))
          .build();
    }
    Optional<GroupModel> requestGroup = topLevelGroup.get().getSubGroupsStream(groupName, true, 0, 1).findFirst();
    if (requestGroup.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse(String.format("Client does not have %s group", request.group)))
          .build();
    }
    if (request.add) {
      if (user.isMemberOf(requestGroup.get())) {
        return Response.ok(new SuccessResponse("user already member of group")).build();
      }
      user.joinGroup(requestGroup.get());
      return Response.status(Response.Status.CREATED).entity(new SuccessResponse("added to group"))
          .build();
    } else {
      if (!user.isMemberOf(requestGroup.get())) {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(new ErrorResponse("user not member of group"))
            .build();
      }
      user.leaveGroup(requestGroup.get());
      return Response.status(Response.Status.OK).entity(new SuccessResponse("removed from group"))
          .build();
    }
  }

  public void checkForServiceAccount(AuthenticationManager.AuthResult authResult) throws ResourceProviderException {
    if (authResult == null) {
      throw new ResourceProviderException("invalid session", Response.Status.UNAUTHORIZED);
    }
    Object serviceAccount = authResult.getToken().getOtherClaims().get("client_id");
    if (serviceAccount == null) {
      throw new ResourceProviderException("not a service account token", Response.Status.UNAUTHORIZED);
    }
    String clientId = authResult.getToken().getIssuedFor();
    if (!clientId.equals(serviceAccount)) {
      throw new ResourceProviderException("client id and authorized party do not match", Response.Status.UNAUTHORIZED);
    }
    RealmModel realm = session.getContext().getRealm();
    ClientModel client = realm.getClientByClientId(clientId);
    if (client == null) {
      throw new ResourceProviderException("client not found", Response.Status.BAD_REQUEST);
    }
  }

  public static class ResourceRequest {
    @JsonProperty("redirect_uris")
    private List<String> redirectUris;
  }

  public static class MakeAdminRequest {
    @JsonProperty("username")
    private String username;
  }

  public static class AddToGroupRequest {
    @JsonProperty("username")
    private String username;
    @JsonProperty("group")
    private String group;
    @JsonProperty("add")
    private boolean add;
  }

  public static class ErrorResponse {
    @JsonProperty("error")
    private String error;

    public ErrorResponse(String error) {
      this.error = error;
    }
  }

  public static class SuccessResponse {
    @JsonProperty("message")
    private String message;

    public SuccessResponse(String message) {
      this.message = message;
    }
  }

  public static class HasAdminResponse {
    @JsonProperty("hasAdmin")
    private boolean message;

    public HasAdminResponse(boolean hasAdmin) {
      this.message = hasAdmin;
    }
  }

  public static class ResourceProviderException extends Exception {
    private String message;
    private Response.Status status;

    public ResourceProviderException(String message, Response.Status status) {
      this.message = message;
      this.status = status;
    }
  }
}