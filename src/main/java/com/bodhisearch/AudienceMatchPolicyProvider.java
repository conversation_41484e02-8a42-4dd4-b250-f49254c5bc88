package com.bodhisearch;

import static org.keycloak.TokenVerifier.IS_ACTIVE;

import java.security.PublicKey;

import org.jboss.logging.Logger;
import org.keycloak.TokenVerifier;
import org.keycloak.TokenVerifier.Predicate;
import org.keycloak.TokenVerifier.RealmUrlCheck;
import org.keycloak.authorization.policy.evaluation.Evaluation;
import org.keycloak.authorization.policy.provider.PolicyProvider;
import org.keycloak.common.VerificationException;
import org.keycloak.crypto.KeyUse;
import org.keycloak.crypto.KeyWrapper;
import org.keycloak.exceptions.TokenVerificationException;
import org.keycloak.jose.jws.JWSInput;
import org.keycloak.jose.jws.JWSInputException;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.JsonWebToken;
import org.keycloak.services.Urls;

public class AudienceMatchPolicyProvider implements PolicyProvider {
  private static final Logger logger = Logger.getLogger(AudienceMatchPolicyProvider.class);

  public AudienceMatchPolicyProvider() {
  }

  @Override
  public void evaluate(Evaluation evaluation) {
    KeycloakSession session = evaluation.getAuthorizationProvider().getKeycloakSession();
    String tokenString = session.getContext().getRequestHeaders().getHeaderString("Authorization");
    if (tokenString == null || tokenString.isEmpty()) {
      logger.error("No Authorization header found, denying token exchange request");
      evaluation.deny();
      return;
    }
    if (!tokenString.startsWith("Bearer ")) {
      logger.error("Invalid Authorization header found, denying token exchange request");
      evaluation.deny();
      return;
    }
    tokenString = tokenString.substring(7);
    String audience = session.getContext().getHttpRequest().getDecodedFormParameters().getFirst("audience");
    try {
      verifyToken(session, tokenString, audience);
      evaluation.grant();
    } catch (VerificationException e) {
      logger.error("Error verifying token", e);
      logger.error("Invalid token found, denying token exchange request");
      evaluation.deny();
      return;
    }
  }

  public static AccessToken verifyToken(KeycloakSession session, String tokenString, String expectedAudience)
      throws VerificationException {
    RealmModel realm = session.getContext().getRealm();
    String issuer = Urls.realmIssuer(session.getContext().getUri().getBaseUri(), realm.getName());

    JWSInput jws;
    try {
      jws = new JWSInput(tokenString);
    } catch (JWSInputException e) {
      throw new VerificationException("Failed to parse token", e);
    }
    KeyWrapper key = session.keys().getKey(realm, jws.getHeader().getKeyId(), KeyUse.SIG,
        jws.getHeader().getAlgorithm().name());
    if (key == null) {
      throw new VerificationException("Public key not found");
    }
    PublicKey publicKey = (PublicKey) key.getPublicKey();

    return verifyTokenWith(tokenString, issuer, expectedAudience, publicKey);
  }

  public static AccessToken verifyTokenWith(String tokenString, String issuer, String client, PublicKey publicKey)
      throws VerificationException {
    return TokenVerifier.create(tokenString, AccessToken.class)
        .withChecks(
            IS_ACTIVE,
            new RealmUrlCheck(issuer),
            new Predicate<JsonWebToken>() {
              @Override
              public boolean test(JsonWebToken t) throws VerificationException {
                Object clientId = t.getOtherClaims().get("client_id");
                if (clientId == null) {
                  throw new TokenVerificationException(t,
                      "token is not a service account token, client_id claim is missing");
                }
                if (!clientId.equals(client)) {
                  throw new TokenVerificationException(t,
                      "token client does not match expected client");
                }
                return true;
              }
            })
        .publicKey(publicKey)
        .verify()
        .getToken();
  }

  @Override
  public void close() {
  }
}