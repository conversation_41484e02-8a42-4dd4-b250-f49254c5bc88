package com.bodhisearch;

import java.util.HashMap;
import java.util.Map;

import org.keycloak.Config;
import org.keycloak.authorization.AuthorizationProvider;
import org.keycloak.authorization.model.Policy;
import org.keycloak.authorization.policy.provider.PolicyProvider;
import org.keycloak.authorization.policy.provider.PolicyProviderFactory;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import org.keycloak.representations.idm.authorization.PolicyRepresentation;

public class AudienceMatchPolicyProviderFactory
        implements PolicyProviderFactory<AudienceMatchRepresentation> {
    public static final String ID = "audience-match";
    private AudienceMatchPolicyProvider provider = new AudienceMatchPolicyProvider();

    @Override
    public PolicyProvider create(AuthorizationProvider authorization) {
        return provider;
    }

    @Override
    public void init(Config.Scope config) {
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {
    }

    @Override
    public void close() {
    }

    @Override
    public String getId() {
        return ID;
    }

    @Override
    public String getName() {
        return "Audience Match";
    }

    @Override
    public String getGroup() {
        return "Identity Based";
    }

    @Override
    public PolicyProvider create(KeycloakSession session) {
        return provider;
    }

    @Override
    public AudienceMatchRepresentation toRepresentation(Policy policy,
            AuthorizationProvider authorization) {
        AudienceMatchRepresentation repr = new AudienceMatchRepresentation();
        return repr;
    }

    @Override
    public Class<AudienceMatchRepresentation> getRepresentationType() {
        return AudienceMatchRepresentation.class;
    }

    @Override
    public void onCreate(Policy policy, AudienceMatchRepresentation representation,
            AuthorizationProvider authorization) {
        updatePolicy(policy, representation);
    }

    @Override
    public void onUpdate(Policy policy, AudienceMatchRepresentation representation,
            AuthorizationProvider authorization) {
        updatePolicy(policy, representation);
    }

    @Override
    public void onImport(Policy policy, PolicyRepresentation representation, AuthorizationProvider authorization) {
        policy.setConfig(representation.getConfig());
    }

    private void updatePolicy(Policy policy, AudienceMatchRepresentation represenation) {
        Map<String, String> config = new HashMap<>(policy.getConfig());
        config.remove("code"); // #TODO: remove this once we have default implementation ready
        policy.setConfig(config);
    }
}