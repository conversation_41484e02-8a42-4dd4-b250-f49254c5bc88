<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <root level="info">
    <appender-ref ref="STDOUT" />
  </root>

  <logger name="org.testcontainers" level="DEBUG" />
  <logger name="org.apache.http" level="INFO" />
  <logger name="dasniko.testcontainers.keycloak" level="DEBUG"/>
  <logger name="com.github.dockerjava" level="WARN" />
  <logger name="com.bodhisearch" level="DEBUG" />
  <logger name="org.keycloak" level="DEBUG" />
</configuration>