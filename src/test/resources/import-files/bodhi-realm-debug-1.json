{"realm": "bodhi", "enabled": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "clients": [{"clientId": "resource-abcd", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "realm-management", "name": "${client_realm-management}", "authorizationSettings": {"resources": [{"name": "client.resource.$resource-abcd", "type": "Client"}], "policies": [{"name": "audience-match-policy", "type": "audience-match", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {}}, {"name": "token-exchange.permission.client.$resource-abcd", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.$resource-abcd\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"audience-match-policy\"]"}}], "scopes": [{"name": "token-exchange"}]}}]}