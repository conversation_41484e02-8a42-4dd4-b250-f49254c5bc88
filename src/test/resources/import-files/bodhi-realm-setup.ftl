{
  "realm": "bodhi",
  "enabled": true,
  "registrationEmailAsUsername": true,
  "rememberMe": true,
  "verifyEmail": true,
  "loginWithEmailAllowed": true,
  "clientScopes": [
    {
      "name": "roles",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "true",
        "gui.order": "",
        "consent.screen.text": ""
      }
    },
    {
      "name": "scope_token_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false",
        "gui.order": "",
        "consent.screen.text": ""
      }
    },
    {
      "name": "scope_token_power_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false",
        "gui.order": "",
        "consent.screen.text": ""
      }
    }
  ],
  "clients": [
<#list resources as resource>
    {
      "clientId": "${resource}",
      "enabled": true,
      "clientAuthenticatorType": "client-secret",
      "secret": "change-me",
      "redirectUris": ["http://localhost/callback"],
      "webOrigins": ["+"],
      "bearerOnly": false,
      "consentRequired": false,
      "standardFlowEnabled": true,
      "implicitFlowEnabled": false,
      "directAccessGrantsEnabled": true,
      "serviceAccountsEnabled": true,
      "publicClient": false,
      "protocol": "openid-connect",
      "fullScopeAllowed": false
    }<#sep>,
</#sep></#list>,
<#list clients as client>
    {
      "clientId": "${client}",
      "enabled": true,
      "clientAuthenticatorType": "client-secret",
      "secret": "change-me",
      "redirectUris": ["*"],
      "webOrigins": ["*"],
      "bearerOnly": false,
      "consentRequired": false,
      "standardFlowEnabled": true,
      "implicitFlowEnabled": false,
      "directAccessGrantsEnabled": true,
      "serviceAccountsEnabled": false,
      "publicClient": true,
      "protocol": "openid-connect",
      "fullScopeAllowed": false
    }<#sep>,
</#sep></#list>,
    {
      "clientId": "realm-management",
      "name": "${'${'}client_realm-management${'}'}",
      "enabled": true,
      "authorizationServicesEnabled": true,
      "serviceAccountsEnabled": true,
      "authorizationSettings": {
        "allowRemoteResourceManagement": true,
        "policyEnforcementMode": "ENFORCING",
        "resources": [
          {
            "name": "client.resource.${r'${client.clientId}'}",
            "type": "Client",
            "ownerManagedAccess": false,
            "attributes": {},
            "uris": [],
            "scopes": [
              {
                "name": "view"
              },
              {
                "name": "manage"
              },
              {
                "name": "configure"
              },
              {
                "name": "map-roles"
              },
              {
                "name": "map-roles-client-scope"
              },
              {
                "name": "map-roles-composite"
              }
            ]
          }
        ],
        "policies": [
          {
            "name": "Default Policy",
            "description": "A policy that grants access only for users within this realm",
            "type": "role",
            "logic": "POSITIVE",
            "decisionStrategy": "AFFIRMATIVE",
            "config": {
              "roles": "[{\"id\":\"realm-management/realm-admin\",\"required\":false}]"
            }
          }
        ],
        "scopes": [
          {
            "name": "view"
          },
          {
            "name": "manage"
          },
          {
            "name": "configure"
          },
          {
            "name": "map-roles"
          },
          {
            "name": "map-roles-client-scope"
          },
          {
            "name": "map-roles-composite"
          }
        ]
      }
    }
  ],
  "roles": {
    "client": {
      <#assign groupResources = resources?filter(item -> !skipGroup?seq_contains(item))>
      <#list groupResources as resource>
      "${resource}": [
        <#list clientRoles as clientRole>
        {
          "name": "${clientRole}",
          "composite": false,
          "clientRole": true
        }<#sep>,</#sep>
        </#list>
      ]<#sep>,</#sep>
      </#list>
    }
  },
  "users": [
    <#list users as user>
    {
      "username": "${user.email}",
      "email": "${user.email}",
      "enabled": true,
      "emailVerified": true,
      "firstName": "${user.firstName}",
      "lastName": "${user.lastName}",
      "credentials": [
        {
          "type": "password",
          "value": "pass"
        }
      ],
      "groups": [
        <#list user.groups as group>
        "${group}"<#sep>,</#sep>
        </#list>
      ]
    }<#sep>,</#sep>
    </#list>
  ],
  "groups": [
    <#list groupResources as resource>
    {
      "name": "users-${resource}",
      "path": "/users-${resource}",
      "subGroups": [
        <#list userGroups as userGroup>
        {
          "name": "${userGroup.name}",
          "path": "/users-${resource}/${userGroup.name}",
          "clientRoles": {
            "${resource}": [
              <#list userGroup.roles as role>
              "${role}"<#sep>,</#sep>
              </#list>
            ]
          }
        }<#sep>,</#sep>
        </#list>
      ]
    }<#sep>,</#sep>
    </#list>
  ],
  "defaultDefaultClientScopes": [
  ],
  "defaultOptionalClientScopes": [
    "offline_access",
    "address",
    "phone",
    "microprofile-jwt",
    "acr",
    "email",
    "profile",
    "role_list",
    "roles",
    "web-origins",
    "scope_token_user",
    "scope_token_power_user"
  ]
}
