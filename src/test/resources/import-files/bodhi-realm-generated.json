{"realm": "bodhi", "enabled": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "clientScopes": [{"name": "roles", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"name": "scope_token_user", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}}, {"name": "scope_token_power_user", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}}], "clients": [{"clientId": "resource-abcd", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-wxyz", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-empty", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-make-first-admin", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-missing-group", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-missing-token-exchange-permission", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "client-lmno", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["*"], "webOrigins": ["*"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "realm-management", "name": "${client_realm-management}", "enabled": true}], "roles": {"client": {"resource-abcd": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}], "resource-wxyz": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}], "resource-empty": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}], "resource-make-first-admin": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}], "resource-missing-token-exchange-permission": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}]}}, "users": [{"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "New", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-abcd/users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Power", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-abcd/power-users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Manager", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-abcd/managers"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Admin", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-abcd/admins"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Other", "credentials": [{"type": "password", "value": "pass"}], "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Other", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-wxyz/users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Other", "lastName": "Manager", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-wxyz/managers"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Admin", "credentials": [{"type": "password", "value": "pass"}], "groups": ["/users-resource-wxyz/admins"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Some", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Some", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "groups": []}], "groups": [{"name": "users-resource-abcd", "path": "/users-resource-abcd", "subGroups": [{"name": "users", "path": "/users-resource-abcd/users", "clientRoles": {"resource-abcd": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-abcd/power-users", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-abcd/managers", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-abcd/admins", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}, {"name": "users-resource-wxyz", "path": "/users-resource-wxyz", "subGroups": [{"name": "users", "path": "/users-resource-wxyz/users", "clientRoles": {"resource-wxyz": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-wxyz/power-users", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-wxyz/managers", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-wxyz/admins", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}, {"name": "users-resource-empty", "path": "/users-resource-empty", "subGroups": [{"name": "users", "path": "/users-resource-empty/users", "clientRoles": {"resource-empty": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-empty/power-users", "clientRoles": {"resource-empty": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-empty/managers", "clientRoles": {"resource-empty": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-empty/admins", "clientRoles": {"resource-empty": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}, {"name": "users-resource-make-first-admin", "path": "/users-resource-make-first-admin", "subGroups": [{"name": "users", "path": "/users-resource-make-first-admin/users", "clientRoles": {"resource-make-first-admin": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-make-first-admin/power-users", "clientRoles": {"resource-make-first-admin": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-make-first-admin/managers", "clientRoles": {"resource-make-first-admin": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-make-first-admin/admins", "clientRoles": {"resource-make-first-admin": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}, {"name": "users-resource-missing-token-exchange-permission", "path": "/users-resource-missing-token-exchange-permission", "subGroups": [{"name": "users", "path": "/users-resource-missing-token-exchange-permission/users", "clientRoles": {"resource-missing-token-exchange-permission": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-missing-token-exchange-permission/power-users", "clientRoles": {"resource-missing-token-exchange-permission": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-missing-token-exchange-permission/managers", "clientRoles": {"resource-missing-token-exchange-permission": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-missing-token-exchange-permission/admins", "clientRoles": {"resource-missing-token-exchange-permission": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}], "defaultDefaultClientScopes": [], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt", "acr", "email", "profile", "role_list", "roles", "web-origins", "scope_token_user", "scope_token_power_user"]}