package com.bodhisearch;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import dasniko.testcontainers.keycloak.KeycloakContainer;

@Testcontainers
public class CliDebugTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(CliDebugTest.class);

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.2.5")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
  // .withDebugFixedPort(8787, true)
  ;

  @BeforeAll
  public static void importConfigs() {
    // Wait a bit to ensure Keycloak is fully ready
    try {
      LOGGER.info("Waiting for Keycloak to be fully ready...");
      Thread.sleep(3000); // 3 seconds initial wait
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException("Interrupted while waiting for Keycloak", e);
    }

    importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(),
        "./src/test/resources/import-files/bodhi-realm-debug-1.json");
  }

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    int maxRetries = 5;
    int retryDelay = 5000; // 5 seconds

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        LOGGER.info("Attempting to import configuration (attempt {}/{})", attempt, maxRetries);

        String[] command = { "java", "-jar",
            "tools/keycloak-config-cli-26.1.0.jar",
            "--import.files.locations=" + filename,
            "--keycloak.url=" + keycloakUrl,
            "--keycloak.user=" + username,
            "--keycloak.password=" + password,
            "--logging.level.keycloak-config-cli=debug"
        };

        Process process = Runtime.getRuntime().exec(command);
        BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        String line;

        StringBuilder stdout = new StringBuilder();
        StringBuilder stderr = new StringBuilder();

        while ((line = stdoutReader.readLine()) != null) {
          LOGGER.info(line);
          stdout.append(line).append("\n");
        }

        while ((line = stderrReader.readLine()) != null) {
          LOGGER.error(line);
          stderr.append(line).append("\n");
        }

        process.waitFor();
        int exitCode = process.exitValue();

        if (exitCode == 0) {
          LOGGER.info("CLI executed successfully on attempt {}", attempt);
          return; // Success, exit the retry loop
        } else {
          String errorOutput = stderr.toString();
          if (attempt < maxRetries && (errorOutput.contains("Connection refused") || errorOutput.contains("ConnectException"))) {
            LOGGER.warn("Connection failed on attempt {}/{}, retrying in {} ms...", attempt, maxRetries, retryDelay);
            Thread.sleep(retryDelay);
            continue; // Retry
          } else {
            String errMsg = "CLI execution failed with exit code: " + exitCode + "\nStderr: " + errorOutput;
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
          }
        }
      } catch (IOException | InterruptedException e) {
        if (attempt < maxRetries) {
          LOGGER.warn("Exception on attempt {}/{}, retrying in {} ms...", attempt, maxRetries, retryDelay, e);
          try {
            Thread.sleep(retryDelay);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted during retry", ie);
          }
        } else {
          LOGGER.error("Failed after {} attempts", maxRetries, e);
          throw new RuntimeException(e);
        }
      }
    }
  }

  @Test
  public void test() {
    System.out.println("Hello, World!");
  }
}
