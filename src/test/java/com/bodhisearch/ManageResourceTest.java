package com.bodhisearch;

import org.junit.jupiter.api.Test;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;

public class ManageResourceTest extends BaseTest {
  @Test
  public void testResourceCannotListClients() {
    JsonPath response = registerClient();
    String clientId = response.getString("client_id");
    String clientSecret = response.getString("client_secret");
    String resourceToken = getTokenForClient(clientId, clientSecret);
    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/%s/clients", keycloak.getAuthServerUrl(), REALM))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceCannotViewSelf() {
    JsonPath response = registerClient();
    String clientId = response.getString("client_id");
    String clientSecret = response.getString("client_secret");
    String resourceToken = getTokenForClient(clientId, clientSecret);
    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceManage() {
    JsonPath response = registerClient();
    String clientId = response.getString("client_id");
    String clientSecret = response.getString("client_secret");
    String resourceToken = getTokenForClient(clientId, clientSecret);
    given()
        .header("Authorization", "Bearer " + resourceToken)
        .contentType(ContentType.JSON)
        .body("{\"name\": \"Updated Name\"}")
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceCannotConfigureScope() {
    JsonPath response = registerClient();
    String clientId = response.getString("client_id");
    String clientSecret = response.getString("client_secret");
    String resourceToken = getTokenForClient(clientId, clientSecret);
    String scopeId = realm.clientScopes().findAll().stream().filter(scope -> scope.getName().equals("phone"))
        .findFirst().get().getId();
    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s/default-client-scopes/%s", keycloak.getAuthServerUrl(),
            clientId, scopeId))
        .then()
        .statusCode(403);
  }
}
