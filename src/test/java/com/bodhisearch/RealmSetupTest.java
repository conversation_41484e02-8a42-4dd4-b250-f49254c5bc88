package com.bodhisearch;

import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;

public class RealmSetupTest extends BaseTest {
  private String resourceAdminUrl;

  @BeforeEach
  public void setUp() {
    resourceAdminUrl = String.format("%s/realms/%s/%s/clients/make-resource-admin",
        keycloak.getAuthServerUrl(), REALM, PROVIDER_ID);
  }

  @Test
  public void testDefaultScopesAreOptional() {
    // Register a new resource
    JsonPath jsonPath = registerClient();
    String clientId = jsonPath.getString("client_id");
    String clientSecret = jsonPath.getString("client_secret");
    assertNotNull(clientId, "Client ID should not be null");
    assertNotNull(clientSecret, "Client secret should not be null");

    // Make <EMAIL> a resource admin
    String serviceAccountToken = getTokenForClient(clientId, clientSecret);
    given()
        .header("Authorization", "Bearer " + serviceAccountToken)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", "<EMAIL>"))
        .when()
        .post(resourceAdminUrl)
        .then()
        .statusCode(201);

    String accessToken = getUserTokenWith(clientId, clientSecret, tokenUrl, "<EMAIL>", "pass");
    // Decode and verify token claims
    // System.out.println(accessToken);
    DecodedJWT jwt = JWT.decode(accessToken);

    // Verify that optional scopes are not present in token
    List<String> missingClaims = Arrays.asList(
        "acr",
        "allowed-origins",
        "resource_access",
        "roles",
        "web-origins",
        "email_verified",
        "name",
        "preferred_username",
        "given_name",
        "family_name",
        "email");
    for (String claim : missingClaims) {
      assertTrue(jwt.getClaim(claim).isMissing(), claim + " claim should not be present");
    }
  }

  @Test
  public void testResourceCreatedWithTestPrefix() {
    // Register a new resource
    JsonPath jsonPath = given()
        .contentType(ContentType.JSON)
        .body("{\"redirect_uris\": [\"http://bodhiapp.localhost/app/callback\"]}")
        .when()
        .post(String.format("%s?live_test=true", registerClientUrl))
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();
    String clientId = jsonPath.getString("client_id");
    assertNotNull(clientId, "Client ID should not be null");
    assertTrue(clientId.startsWith("test-resource-"));
  }
}
