package com.bodhisearch;

import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;



import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.keycloak.TokenVerifier;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.common.VerificationException;
import org.keycloak.jose.jws.JWSInput;
import org.keycloak.jose.jws.JWSInputException;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.KeysMetadataRepresentation.KeyMetadataRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
// import io.restassured.filter.log.LogDetail;
// import io.restassured.filter.log.RequestLoggingFilter;
// import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.specification.RequestSpecification;

@Testcontainers
public class BaseTest {
  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi";
  public static final String RESOURCE_ABCD = "resource-abcd";
  public static final String RESOURCE_SECRET = "change-me";
  public static final String RESOURCE_WXYZ = "resource-wxyz";
  public static final String CLIENT_LMNO = "client-lmno";
  public static final String CLIENT_SECRET = "change-me";
  public static final String RESOURCE_EMPTY = "resource-empty";
  public static final String ROLE_RESOURCE_ADMIN = "resource_admin";
  public static final String ROLE_RESOURCE_MANAGER = "resource_manager";
  public static final String ROLE_RESOURCE_POWER_USER = "resource_power_user";
  public static final String ROLE_RESOURCE_USER = "resource_user";
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseTest.class);
  protected static String tokenUrl;
  protected static String registerClientUrl;
  protected static Keycloak admin;
  protected static RealmResource realm;
  protected static String resourceAdminUrl;
  protected static String addToGroupUrl;
  protected static String hasAdminUrl;

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.2.5")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
      .withEnv("APP_ENV", "test")
  // .withDebugFixedPort(8787, true)
  ;

  @BeforeAll
  public static void importConfigs() {
    // Wait a bit to ensure Keycloak is fully ready
    try {
      LOGGER.info("Waiting for Keycloak to be fully ready...");
      Thread.sleep(3000); // 3 seconds initial wait
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException("Interrupted while waiting for Keycloak", e);
    }

    // Temporarily skip CLI import and use manual realm creation for testing
    LOGGER.info("Creating realm manually for testing...");
    admin = keycloak.getKeycloakAdminClient();

    // Create the realm manually
    createRealmManually();

    realm = admin.realm(REALM);
    tokenUrl = String.format("%s/realms/%s/protocol/openid-connect/token", keycloak.getAuthServerUrl(), REALM);
    registerClientUrl = String.format("%s/realms/%s/bodhi/clients", keycloak.getAuthServerUrl(), REALM);

    resourceAdminUrl = buildResourceAdminUrl(REALM, PROVIDER_ID);
    addToGroupUrl = buildAddToGroupUrl(REALM, PROVIDER_ID);
    hasAdminUrl = buildHasAdminUrl(REALM, PROVIDER_ID);
  }

  public static RequestSpecification given() {
    return RestAssured.given().relaxedHTTPSValidation().redirects().follow(false)
    // .filter(new RequestLoggingFilter(LogDetail.ALL))
    // .filter(new ResponseLoggingFilter(LogDetail.ALL))
    ;
  }

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    int maxRetries = 5;
    int retryDelay = 5000; // 5 seconds

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        LOGGER.info("Attempting to import configuration (attempt {}/{})", attempt, maxRetries);

        String[] command = { "java", "-jar",
            "tools/keycloak-config-cli-26.1.0.jar",
            "--import.files.locations=" + filename,
            "--keycloak.url=" + keycloakUrl,
            "--keycloak.user=" + username,
            "--keycloak.password=" + password,
        };

        Process process = Runtime.getRuntime().exec(command);
        BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        String line;

        StringBuilder stdout = new StringBuilder();
        StringBuilder stderr = new StringBuilder();

        while ((line = stdoutReader.readLine()) != null) {
          LOGGER.info(line);
          stdout.append(line).append("\n");
        }

        while ((line = stderrReader.readLine()) != null) {
          LOGGER.error(line);
          stderr.append(line).append("\n");
        }

        process.waitFor();
        int exitCode = process.exitValue();

        if (exitCode == 0) {
          LOGGER.info("CLI executed successfully on attempt {}", attempt);
          return; // Success, exit the retry loop
        } else {
          String errorOutput = stderr.toString();
          if (attempt < maxRetries && (errorOutput.contains("Connection refused") || errorOutput.contains("ConnectException"))) {
            LOGGER.warn("Connection failed on attempt {}/{}, retrying in {} ms...", attempt, maxRetries, retryDelay);
            Thread.sleep(retryDelay);
            continue; // Retry
          } else {
            String errMsg = "CLI execution failed with exit code: " + exitCode + "\nStderr: " + errorOutput;
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
          }
        }
      } catch (IOException | InterruptedException e) {
        if (attempt < maxRetries) {
          LOGGER.warn("Exception on attempt {}/{}, retrying in {} ms...", attempt, maxRetries, retryDelay, e);
          try {
            Thread.sleep(retryDelay);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted during retry", ie);
          }
        } else {
          LOGGER.error("Failed after {} attempts", maxRetries, e);
          throw new RuntimeException(e);
        }
      }
    }
  }

  public String[] refreshTokenFlow(String clientId, String clientSecret, String tokenUrl, String refreshToken) {
    Response response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "refresh_token")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("refresh_token", refreshToken)
        .when()
        .post(tokenUrl);
    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return new String[] { response.jsonPath().getString("access_token"),
        response.jsonPath().getString("refresh_token") };
  }

  public static Response getUserTokenResponseWith(String clientId, String clientSecret, String tokenUrl,
      String username,
      String password, List<String> scopes) {
    RequestSpecification request = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "password")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("username", username)
        .formParam("password", password)
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")));
    Response response = request
        .when()
        .post(tokenUrl);
    return response;
  }

  public static String getUserTokenWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password) {
    return getUserTokenWith(clientId, clientSecret, tokenUrl, username, password, Arrays.asList("openid"));
  }

  public static String getUserTokenWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password, List<String> scopes) {
    Response response = getUserTokenResponseWith(clientId, clientSecret, tokenUrl, username, password, scopes);

    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return response.jsonPath().getString("access_token");
  }

  public static String[] getUserTokenPairWith(String clientId, String clientSecret, String tokenUrl, String username,
      String password, List<String> scopes) {
    Response response = getUserTokenResponseWith(clientId, clientSecret, tokenUrl, username, password, scopes);

    if (response.getStatusCode() != 200) {
      System.out.println("Error response: " + response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    String[] tokenPair = new String[] { response.jsonPath().getString("access_token"),
        response.jsonPath().getString("refresh_token") };
    return tokenPair;
  }

  protected static String getTokenForClient(String clientId, String clientSecret) {
    Response response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "client_credentials")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .when()
        .post(tokenUrl);
    return response
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath().getString("access_token");
  }

  protected String[] exchangeToOfflineToken(String clientId, String clientSecret, String subjectToken,
      List<String> scopes) {
    JsonPath response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("client_id", clientId)
        .formParam("client_secret", clientSecret)
        .formParam("subject_token", subjectToken)
        .formParam("requested_token_type", "urn:ietf:params:oauth:token-type:refresh_token")
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")))
        .post(tokenUrl)
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath();
    return new String[] { response.getString("access_token"), response.getString("refresh_token") };
  }

  protected String exchangeClientResourceToken(String clientId, String sourceToken, String audience,
      String accessToken) {
    return exchangeToken(clientId, sourceToken, audience, accessToken)
        .then()
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .statusCode(200)
        .body("access_token", notNullValue())
        .extract().jsonPath()
        .getString("access_token");
  }

  protected String offlineToken(String clientId, String sourceToken, String audience, String accessToken) {
    return exchangeToken(clientId, sourceToken, audience, accessToken)
        .then()
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .statusCode(200)
        .body("access_token", notNullValue())
        .extract().jsonPath()
        .getString("access_token");
  }

  protected Response exchangeToken(String clientId, String userClientAccessToken, String resource,
      String resourceTokenAsBearer) {
    RequestSpecification request = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("subject_token", userClientAccessToken)
        .formParam("client_id", clientId)
        .formParam("audience", resource)
        .formParam("scope", "openid email profile roles");
    if (resourceTokenAsBearer != null) {
      request.header("Authorization", resourceTokenAsBearer);
    }
    return request
        .when()
        .post(tokenUrl);
  }

  protected static String getResourceToken() {
    return getTokenForClient(RESOURCE_ABCD, RESOURCE_SECRET);
  }

  protected static String getResourceTokenMakeFirstAdmin() {
    return getTokenForClient("resource-make-first-admin", "change-me");
  }

  protected String getOtherResourceToken() {
    return getTokenForClient(RESOURCE_WXYZ, RESOURCE_SECRET);
  }

  protected String getEmptyResourceToken() {
    return getTokenForClient(RESOURCE_EMPTY, RESOURCE_SECRET);
  }

  protected String getOtherUserToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected String getOtherAdminToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected String getResourceAdminUserToken() {
    return getUserTokenWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, "<EMAIL>", "pass");
  }

  protected JWSInput getJws(String accessToken) {
    try {
      return new JWSInput(accessToken);
    } catch (JWSInputException e) {
      throw new RuntimeException(e);
    }
  }

  protected AccessToken decodeToken(String token) {
    try {
      TokenVerifier<AccessToken> verifier = TokenVerifier.create(token, AccessToken.class);
      return verifier.parse().getToken();
    } catch (VerificationException e) {
      throw new RuntimeException("Failed to decode token", e);
    }
  }

  protected KeyMetadataRepresentation getKeyRepr(String userToken)
      throws JsonProcessingException, JsonMappingException {
    String header = userToken.split("\\.")[0];
    byte[] decodedBytes = Base64.getDecoder().decode(header);
    String decodedHeader = new String(decodedBytes, StandardCharsets.UTF_8);
    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonHeader = objectMapper.readTree(decodedHeader);
    String kid = jsonHeader.get("kid").asText();
    KeyMetadataRepresentation keyRepr = realm.keys().getKeyMetadata().getKeys().stream()
        .filter(k -> k.getKid().equals(kid))
        .findFirst().get();
    return keyRepr;
  }

  protected JsonPath registerClient() {
    JsonPath response = given()
        .contentType(ContentType.JSON)
        .body("{\"redirect_uris\": [\"http://bodhiapp.localhost/app/callback\"]}")
        .when()
        .post(registerClientUrl)
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();
    return response;
  }

  protected String[] registerClientAndReturnTokenPair() {
    JsonPath response = registerClient();
    return new String[] { response.getString("client_id"), response.getString("client_secret") };
  }

  protected Response makeResourceAdmin(String resourceAdminUrl, String token, String userToMakeFirstAdmin) {
    return given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", userToMakeFirstAdmin))
        .when()
        .post(resourceAdminUrl);
  }

  protected static String buildResourceAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/make-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildAddToGroupUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/add-user-to-group",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildHasAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/has-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected Response addUserToGroup(String url, String token, String testUser, String group) {
    return given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"%s\", \"add\": true}", testUser, group))
        .when()
        .post(url);
  }

  private static void createRealmManually() {
    try {
      LOGGER.info("Creating comprehensive realm configuration manually...");

      // Create the realm
      createBasicRealm();

      // Create client scopes
      createClientScopes();

      // Create all test clients
      createTestClients();

      // Create users
      createTestUsers();

      // Create groups with role mappings
      createGroupsAndRoles();

      // Configure authorization services for realm-management
      configureAuthorizationServices();

      LOGGER.info("Comprehensive realm configuration completed successfully");

    } catch (Exception e) {
      LOGGER.error("Failed to create realm manually", e);
      throw new RuntimeException("Failed to create realm manually", e);
    }
  }

  private static void createBasicRealm() {
    org.keycloak.representations.idm.RealmRepresentation realmRep = new org.keycloak.representations.idm.RealmRepresentation();
    realmRep.setRealm(REALM);
    realmRep.setEnabled(true);
    realmRep.setRegistrationEmailAsUsername(true);
    realmRep.setRememberMe(true);
    realmRep.setVerifyEmail(true);
    realmRep.setLoginWithEmailAllowed(true);

    admin.realms().create(realmRep);
    LOGGER.info("Basic realm '{}' created successfully", REALM);
  }

  private static void createClientScopes() {
    RealmResource realmResource = admin.realm(REALM);

    // Create roles client scope
    org.keycloak.representations.idm.ClientScopeRepresentation rolesScope = new org.keycloak.representations.idm.ClientScopeRepresentation();
    rolesScope.setName("roles");
    rolesScope.setProtocol("openid-connect");
    rolesScope.setAttributes(java.util.Map.of(
        "include.in.token.scope", "true",
        "display.on.consent.screen", "true"
    ));
    realmResource.clientScopes().create(rolesScope);

    // Create scope_token_user client scope
    org.keycloak.representations.idm.ClientScopeRepresentation userScope = new org.keycloak.representations.idm.ClientScopeRepresentation();
    userScope.setName("scope_token_user");
    userScope.setProtocol("openid-connect");
    userScope.setAttributes(java.util.Map.of(
        "include.in.token.scope", "true",
        "display.on.consent.screen", "false"
    ));
    realmResource.clientScopes().create(userScope);

    // Create scope_token_power_user client scope
    org.keycloak.representations.idm.ClientScopeRepresentation powerUserScope = new org.keycloak.representations.idm.ClientScopeRepresentation();
    powerUserScope.setName("scope_token_power_user");
    powerUserScope.setProtocol("openid-connect");
    powerUserScope.setAttributes(java.util.Map.of(
        "include.in.token.scope", "true",
        "display.on.consent.screen", "false"
    ));
    realmResource.clientScopes().create(powerUserScope);

    LOGGER.info("Client scopes created successfully");
  }

  private static void createTestClients() {
    RealmResource realmResource = admin.realm(REALM);

    // List of resource clients to create
    String[] resourceClients = {
        "resource-abcd", "resource-wxyz", "resource-empty",
        "resource-make-first-admin", "resource-missing-group",
        "resource-missing-token-exchange-permission"
    };

    for (String clientId : resourceClients) {
      createResourceClient(realmResource, clientId);
    }

    // Create client-lmno (public client)
    org.keycloak.representations.idm.ClientRepresentation clientLmno = new org.keycloak.representations.idm.ClientRepresentation();
    clientLmno.setClientId(CLIENT_LMNO);
    clientLmno.setEnabled(true);
    clientLmno.setClientAuthenticatorType("client-secret");
    clientLmno.setSecret(CLIENT_SECRET);
    clientLmno.setRedirectUris(java.util.Arrays.asList("*"));
    clientLmno.setWebOrigins(java.util.Arrays.asList("*"));
    clientLmno.setBearerOnly(false);
    clientLmno.setConsentRequired(false);
    clientLmno.setStandardFlowEnabled(true);
    clientLmno.setDirectAccessGrantsEnabled(true);
    clientLmno.setServiceAccountsEnabled(false);
    clientLmno.setPublicClient(true);
    clientLmno.setProtocol("openid-connect");
    clientLmno.setFullScopeAllowed(false);

    realmResource.clients().create(clientLmno);
    LOGGER.info("Client '{}' created successfully", CLIENT_LMNO);

    LOGGER.info("All test clients created successfully");
  }

  private static void createResourceClient(RealmResource realmResource, String clientId) {
    org.keycloak.representations.idm.ClientRepresentation clientRep = new org.keycloak.representations.idm.ClientRepresentation();
    clientRep.setClientId(clientId);
    clientRep.setEnabled(true);
    clientRep.setClientAuthenticatorType("client-secret");
    clientRep.setSecret("change-me");
    clientRep.setRedirectUris(java.util.Arrays.asList("http://localhost/callback"));
    clientRep.setWebOrigins(java.util.Arrays.asList("+"));
    clientRep.setBearerOnly(false);
    clientRep.setConsentRequired(false);
    clientRep.setStandardFlowEnabled(true);
    clientRep.setDirectAccessGrantsEnabled(true);
    clientRep.setServiceAccountsEnabled(true);
    clientRep.setPublicClient(false);
    clientRep.setProtocol("openid-connect");
    clientRep.setFullScopeAllowed(false);

    realmResource.clients().create(clientRep);

    // Create client roles for this resource client
    createClientRoles(realmResource, clientId);

    LOGGER.info("Resource client '{}' created successfully", clientId);
  }

  private static void createClientRoles(RealmResource realmResource, String clientId) {
    // Find the client by clientId
    org.keycloak.representations.idm.ClientRepresentation client = realmResource.clients()
        .findByClientId(clientId).get(0);

    // Create the four standard roles for each resource client
    String[] roleNames = {ROLE_RESOURCE_USER, ROLE_RESOURCE_POWER_USER, ROLE_RESOURCE_MANAGER, ROLE_RESOURCE_ADMIN};

    for (String roleName : roleNames) {
      org.keycloak.representations.idm.RoleRepresentation role = new org.keycloak.representations.idm.RoleRepresentation();
      role.setName(roleName);
      role.setComposite(false);
      role.setClientRole(true);

      realmResource.clients().get(client.getId()).roles().create(role);
    }

    LOGGER.info("Client roles created for '{}'", clientId);
  }

  private static void createTestUsers() {
    RealmResource realmResource = admin.realm(REALM);

    // Create users for resource-abcd
    createUser(realmResource, "<EMAIL>", "New", "User", "pass");
    createUser(realmResource, "<EMAIL>", "Power", "User", "pass");
    createUser(realmResource, "<EMAIL>", "User", "Manager", "pass");
    createUser(realmResource, "<EMAIL>", "User", "Admin", "pass");

    // Create users for resource-wxyz
    createUser(realmResource, "<EMAIL>", "User", "Other", "pass");
    createUser(realmResource, "<EMAIL>", "Other", "Manager", "pass");
    createUser(realmResource, "<EMAIL>", "User", "Admin", "pass");

    // Create additional test users
    createUser(realmResource, "<EMAIL>", "Test", "User", "pass");
    createUser(realmResource, "<EMAIL>", "Other", "User", "pass");
    createUser(realmResource, "<EMAIL>", "Some", "User", "pass");

    LOGGER.info("Test users created successfully");
  }

  private static void createUser(RealmResource realmResource, String email, String firstName, String lastName, String password) {
    org.keycloak.representations.idm.UserRepresentation user = new org.keycloak.representations.idm.UserRepresentation();
    user.setUsername(email);
    user.setEmail(email);
    user.setEnabled(true);
    user.setEmailVerified(true);
    user.setFirstName(firstName);
    user.setLastName(lastName);

    // Create the user
    jakarta.ws.rs.core.Response response = realmResource.users().create(user);
    String userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");
    response.close();

    // Set password
    org.keycloak.representations.idm.CredentialRepresentation credential = new org.keycloak.representations.idm.CredentialRepresentation();
    credential.setType("password");
    credential.setValue(password);
    credential.setTemporary(false);

    realmResource.users().get(userId).resetPassword(credential);

    LOGGER.info("User '{}' created successfully", email);
  }

  private static void createGroupsAndRoles() {
    RealmResource realmResource = admin.realm(REALM);

    // Create groups for each resource client
    String[] resourceClients = {"resource-abcd", "resource-wxyz", "resource-empty", "resource-make-first-admin", "resource-missing-token-exchange-permission"};

    for (String clientId : resourceClients) {
      createGroupHierarchyForClient(realmResource, clientId);
    }

    // Assign users to groups
    assignUsersToGroups(realmResource);

    LOGGER.info("Groups and role assignments created successfully");
  }

  private static void createGroupHierarchyForClient(RealmResource realmResource, String clientId) {
    // Create parent group
    String parentGroupName = "users-" + clientId;
    org.keycloak.representations.idm.GroupRepresentation parentGroup = new org.keycloak.representations.idm.GroupRepresentation();
    parentGroup.setName(parentGroupName);
    parentGroup.setPath("/" + parentGroupName);

    jakarta.ws.rs.core.Response response = realmResource.groups().add(parentGroup);
    String parentGroupId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");
    response.close();

    // Create subgroups with role mappings
    createSubGroupWithRoles(realmResource, parentGroupId, "users", clientId, new String[]{ROLE_RESOURCE_USER});
    createSubGroupWithRoles(realmResource, parentGroupId, "power-users", clientId, new String[]{ROLE_RESOURCE_USER, ROLE_RESOURCE_POWER_USER});
    createSubGroupWithRoles(realmResource, parentGroupId, "managers", clientId, new String[]{ROLE_RESOURCE_USER, ROLE_RESOURCE_POWER_USER, ROLE_RESOURCE_MANAGER});
    createSubGroupWithRoles(realmResource, parentGroupId, "admins", clientId, new String[]{ROLE_RESOURCE_USER, ROLE_RESOURCE_POWER_USER, ROLE_RESOURCE_MANAGER, ROLE_RESOURCE_ADMIN});

    LOGGER.info("Group hierarchy created for client '{}'", clientId);
  }

  private static void createSubGroupWithRoles(RealmResource realmResource, String parentGroupId, String subGroupName, String clientId, String[] roleNames) {
    // Create subgroup
    org.keycloak.representations.idm.GroupRepresentation subGroup = new org.keycloak.representations.idm.GroupRepresentation();
    subGroup.setName(subGroupName);

    jakarta.ws.rs.core.Response response = realmResource.groups().group(parentGroupId).subGroup(subGroup);
    String subGroupId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");
    response.close();

    // Find the client to get its roles
    org.keycloak.representations.idm.ClientRepresentation client = realmResource.clients().findByClientId(clientId).get(0);

    // Assign client roles to the subgroup
    java.util.List<org.keycloak.representations.idm.RoleRepresentation> rolesToAssign = new java.util.ArrayList<>();
    for (String roleName : roleNames) {
      org.keycloak.representations.idm.RoleRepresentation role = realmResource.clients().get(client.getId()).roles().get(roleName).toRepresentation();
      rolesToAssign.add(role);
    }

    realmResource.groups().group(subGroupId).roles().clientLevel(client.getId()).add(rolesToAssign);

    LOGGER.info("Subgroup '{}' created with roles for client '{}'", subGroupName, clientId);
  }

  private static void assignUsersToGroups(RealmResource realmResource) {
    // Assign users to resource-abcd groups
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-abcd/users");
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-abcd/power-users");
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-abcd/managers");
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-abcd/admins");

    // Assign users to resource-wxyz groups
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-wxyz/users");
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-wxyz/managers");
    assignUserToGroup(realmResource, "<EMAIL>", "/users-resource-wxyz/admins");

    LOGGER.info("Users assigned to groups successfully");
  }

  private static void assignUserToGroup(RealmResource realmResource, String username, String groupPath) {
    try {
      // Find user by username
      java.util.List<org.keycloak.representations.idm.UserRepresentation> users = realmResource.users().search(username);
      if (users.isEmpty()) {
        LOGGER.warn("User '{}' not found for group assignment", username);
        return;
      }

      org.keycloak.representations.idm.UserRepresentation user = users.get(0);

      // Find group by path
      java.util.List<org.keycloak.representations.idm.GroupRepresentation> groups = realmResource.groups().groups();
      org.keycloak.representations.idm.GroupRepresentation targetGroup = findGroupByPath(groups, groupPath);

      if (targetGroup != null) {
        realmResource.users().get(user.getId()).joinGroup(targetGroup.getId());
        LOGGER.info("User '{}' assigned to group '{}'", username, groupPath);
      } else {
        LOGGER.warn("Group '{}' not found for user assignment", groupPath);
      }
    } catch (Exception e) {
      LOGGER.error("Failed to assign user '{}' to group '{}'", username, groupPath, e);
    }
  }

  private static org.keycloak.representations.idm.GroupRepresentation findGroupByPath(java.util.List<org.keycloak.representations.idm.GroupRepresentation> groups, String path) {
    for (org.keycloak.representations.idm.GroupRepresentation group : groups) {
      if (path.equals(group.getPath())) {
        return group;
      }
      // Check subgroups recursively
      if (group.getSubGroups() != null) {
        org.keycloak.representations.idm.GroupRepresentation found = findGroupByPath(group.getSubGroups(), path);
        if (found != null) {
          return found;
        }
      }
    }
    return null;
  }

  private static void configureAuthorizationServices() {
    RealmResource realmResource = admin.realm(REALM);

    // Find the realm-management client
    java.util.List<org.keycloak.representations.idm.ClientRepresentation> clients = realmResource.clients().findByClientId("realm-management");
    if (clients.isEmpty()) {
      LOGGER.warn("realm-management client not found, skipping authorization configuration");
      return;
    }

    org.keycloak.representations.idm.ClientRepresentation realmManagementClient = clients.get(0);

    // Enable authorization services on realm-management client
    realmManagementClient.setAuthorizationServicesEnabled(true);
    realmResource.clients().get(realmManagementClient.getId()).update(realmManagementClient);

    // Create authorization settings
    createAuthorizationSettings(realmResource, realmManagementClient.getId());

    LOGGER.info("Authorization services configured successfully");
  }

  private static void createAuthorizationSettings(RealmResource realmResource, String clientId) {
    try {
      // Get the authorization resource for the realm-management client
      org.keycloak.admin.client.resource.AuthorizationResource authzResource = realmResource.clients().get(clientId).authorization();

      // Create token-exchange scope
      org.keycloak.representations.idm.authorization.ScopeRepresentation tokenExchangeScope = new org.keycloak.representations.idm.authorization.ScopeRepresentation();
      tokenExchangeScope.setName("token-exchange");
      authzResource.scopes().create(tokenExchangeScope);

      // Create resources for each client
      String[] resourceClients = {"resource-abcd", "resource-wxyz", "resource-empty", "resource-make-first-admin", "resource-missing-group"};
      for (String resourceClient : resourceClients) {
        org.keycloak.representations.idm.authorization.ResourceRepresentation resource = new org.keycloak.representations.idm.authorization.ResourceRepresentation();
        resource.setName("client.resource.$" + resourceClient);
        resource.setType("Client");
        authzResource.resources().create(resource);
      }

      // Create audience-match policy
      org.keycloak.representations.idm.authorization.PolicyRepresentation audienceMatchPolicy = new org.keycloak.representations.idm.authorization.PolicyRepresentation();
      audienceMatchPolicy.setName("audience-match-policy");
      audienceMatchPolicy.setType("audience-match");
      audienceMatchPolicy.setLogic(org.keycloak.representations.idm.authorization.Logic.POSITIVE);
      audienceMatchPolicy.setDecisionStrategy(org.keycloak.representations.idm.authorization.DecisionStrategy.UNANIMOUS);
      audienceMatchPolicy.setConfig(new java.util.HashMap<>());
      authzResource.policies().create(audienceMatchPolicy);

      // Create token exchange permission policies for each resource
      for (String resourceClient : resourceClients) {
        org.keycloak.representations.idm.authorization.PolicyRepresentation permissionPolicy = new org.keycloak.representations.idm.authorization.PolicyRepresentation();
        permissionPolicy.setName("token-exchange.permission.client.$" + resourceClient);
        permissionPolicy.setType("scope");
        permissionPolicy.setLogic(org.keycloak.representations.idm.authorization.Logic.POSITIVE);
        permissionPolicy.setDecisionStrategy(org.keycloak.representations.idm.authorization.DecisionStrategy.UNANIMOUS);

        java.util.Map<String, String> config = new java.util.HashMap<>();
        config.put("resources", "[\"client.resource.$" + resourceClient + "\"]");
        config.put("scopes", "[\"token-exchange\"]");
        config.put("applyPolicies", "[\"audience-match-policy\"]");
        permissionPolicy.setConfig(config);

        authzResource.policies().create(permissionPolicy);
      }

      LOGGER.info("Authorization policies and resources created successfully");

    } catch (Exception e) {
      LOGGER.error("Failed to create authorization settings", e);
      // Don't throw exception here as the basic functionality should still work
    }
  }
}
