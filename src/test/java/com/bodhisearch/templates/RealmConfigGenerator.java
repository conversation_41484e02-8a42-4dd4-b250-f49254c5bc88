package com.bodhisearch.templates;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;

public class RealmConfigGenerator {
  public static final List<String> CLIENT_ROLES = Arrays.asList("resource_user", "resource_power_user",
      "resource_manager", "resource_admin");
  public static final List<Group> GROUPS = Arrays.asList(
      new Group("users", Arrays.asList("resource_user")),
      new Group("power-users", Arrays.asList("resource_user", "resource_power_user")),
      new Group("managers", Arrays.asList("resource_user", "resource_power_user", "resource_manager")),
      new Group("admins", Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")));

  public static void main(String[] args) {
    generate("./src/test/resources/import-files/bodhi-realm-generated.json");
  }

  public static void generate(String filename) {
    List<User> users = Arrays.asList(
        new User("<EMAIL>", "New", "User", Arrays.asList()),
        new User("<EMAIL>", "Test", "User", Arrays.asList("/users-resource-abcd/users")),
        new User("<EMAIL>", "Power", "User", Arrays.asList("/users-resource-abcd/power-users")),
        new User("<EMAIL>", "User", "Manager", Arrays.asList("/users-resource-abcd/managers")),
        new User("<EMAIL>", "User", "Admin", Arrays.asList("/users-resource-abcd/admins")),
        new User("<EMAIL>", "User", "Other", Arrays.asList()),
        new User("<EMAIL>", "Other", "User", Arrays.asList("/users-resource-wxyz/users")),
        new User("<EMAIL>", "Other", "Manager",
            Arrays.asList("/users-resource-wxyz/managers")),
        new User("<EMAIL>", "User", "Admin", Arrays.asList("/users-resource-wxyz/admins")),
        new User("<EMAIL>", "Some", "User", Arrays.asList()),
        new User("<EMAIL>", "Some", "User", Arrays.asList()),
        new User("<EMAIL>", "Test", "User", Arrays.asList()),
        new User("<EMAIL>", "Test", "User", Arrays.asList()));

    Map<String, Object> dataModel = new HashMap<>();
    dataModel.put("resources", Arrays.asList("resource-abcd", "resource-wxyz", "resource-empty",
        "resource-make-first-admin", "resource-missing-group", "resource-missing-token-exchange-permission"));
    dataModel.put("clients", Arrays.asList("client-lmno"));
    dataModel.put("users", users);
    dataModel.put("clientRoles", CLIENT_ROLES);
    dataModel.put("userGroups", GROUPS);
    dataModel.put("skipTokenExchange", Arrays.asList("resource-missing-token-exchange-permission"));
    dataModel.put("skipGroup", Arrays.asList("resource-missing-group"));

    Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
    cfg.setClassForTemplateLoading(RealmConfigGenerator.class, "/");

    try {
      Template template = cfg.getTemplate("/import-files/bodhi-realm-setup.ftl");
      try (Writer out = new FileWriter(filename)) {
        template.process(dataModel, out);
      }
    } catch (IOException | TemplateException e) {
      e.printStackTrace();
    }
  }
}
