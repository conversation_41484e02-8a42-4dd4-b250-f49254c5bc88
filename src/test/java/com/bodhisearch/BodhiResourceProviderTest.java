package com.bodhisearch;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleScopeResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import io.restassured.http.ContentType;

public class BodhiResourceProviderTest extends BaseTest {
  private RealmResource realm;

  @BeforeEach
  public void setUp() {
    realm = keycloak.getKeycloakAdminClient().realm(REALM);
  }

  @Test
  public void testMakeFirstAdmin() {
    String clientId = "resource-make-first-admin";
    String token = getResourceTokenMakeFirstAdmin();
    String userToMakeFirstAdmin = "<EMAIL>";
    makeResourceAdmin(resourceAdminUrl, token, userToMakeFirstAdmin)
        .then()
        // .body("$", equalTo(""))
        .statusCode(201);
    ClientRepresentation client = realm.clients().findByClientId(clientId).get(0);
    UserRepresentation user = realm.users().search(userToMakeFirstAdmin).get(0);
    assertNotNull(user);
    UserResource userResource = realm.users().get(user.getId());
    RoleScopeResource clientRoles = userResource.roles().clientLevel(client.getId());
    RoleRepresentation adminRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(ROLE_RESOURCE_ADMIN)).collect(Collectors.toList()).get(0);
    assertNotNull(adminRole);
    RoleRepresentation managerRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(ROLE_RESOURCE_MANAGER)).collect(Collectors.toList()).get(0);
    assertNotNull(managerRole);
    RoleRepresentation userRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(ROLE_RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(userRole);
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    Optional<GroupRepresentation> adminGroup = userGroups.stream().filter(g -> g.getName().equals("admins"))
        .findFirst();
    assertTrue(adminGroup.isPresent());
    String adminGroupPath = String.format("/users-%s/admins", clientId);
    assertEquals(adminGroupPath, adminGroup.get().getPath());
    assertEquals(1, userGroups.size());
  }

  @Test
  public void testMakeFirstAdminUnauthorized() {
    given()
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminErrorIfNotServiceToken() {
    String token = getResourceAdminUserToken();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("not a service account token"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminInvalidToken() {
    String token = getEmptyResourceToken() + "foobar";
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminFailsIfAlreadyHaveAdmin() {
    String token = getResourceToken();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource already has a admin user"))
        .statusCode(400);
  }

  @Test
  public void testMakeFirstAdminFailsIfTopLevelGroupNotConfigured() {
    String token = getTokenForClient("resource-missing-group", "change-me");
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource group not configured for client"))
        .statusCode(500);
  }

  @Test
  public void testMakeFirstAdminUserNotFound() {
    String token = getEmptyResourceToken();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("user not found"))
        .statusCode(400);
  }

  @Test
  public void testAddUserToGroupByResourceAdmin() {
    String token = getResourceAdminUserToken();
    String testUser = "<EMAIL>";
    addUserToGroup(addToGroupUrl, token, testUser, "users")
        .then()
        .body("message", equalTo("added to group"))
        .statusCode(201);
    ClientRepresentation client = realm.clients().findByClientId(RESOURCE_ABCD).get(0);
    UserRepresentation user = realm.users().search(testUser).get(0);
    RoleScopeResource clientRoles = realm.users().get(user.getId()).roles().clientLevel(client.getId());
    RoleRepresentation clientRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(ROLE_RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(clientRole);
    UserResource userResource = realm.users().get(user.getId());
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    assertEquals(String.format("/users-%s/users", RESOURCE_ABCD), userGroups.get(0).getPath());
    assertEquals(1, userGroups.size());
  }

  @Test
  public void testAddUserToGroupAlreadyMember() {
    String token = getResourceAdminUserToken();
    String approveUser = "<EMAIL>";
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"users\", \"add\": true}", approveUser))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("message", equalTo("user already member of group"))
        .statusCode(200);
  }

  @Test
  public void testAddUserToGroupUnauthorized() {
    given()
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\", \"group\": \"users\", \"add\": true}")
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testAddUserToGroupByOtherClientAdmin() {
    String token = getOtherAdminToken();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\", \"group\": \"users\", \"add\": true}")
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("User does not have resource-admin role"))
        .statusCode(403);
  }

  @Test
  public void testHasAdminOnEmptyClient() {
    String token = getEmptyResourceToken();
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .body("error", nullValue())
        .statusCode(200)
        .body("hasAdmin", equalTo(false));
  }

  @Test
  public void testHasAdminOnClientWithAdmin() {
    String token = getResourceToken();
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(200)
        .body("hasAdmin", equalTo(true));
  }

  @Test
  public void testHasAdminReturnsUnauthorizedIfCalledWithNonServiceToken() {
    String token = getResourceAdminUserToken();
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(401)
        .body("error", equalTo("not a service account token"));
  }
}