package com.bodhisearch;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.endsWith;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.startsWith;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Test;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;

import io.restassured.path.json.JsonPath;

public class RegisterResourceTest extends BaseTest {

  @Test
  public void testRegisterResource() {
    JsonPath jsonPath = registerClient();
    String clientId = jsonPath.getString("client_id");
    String clientSecret = jsonPath.getString("client_secret");
    String publicKey = jsonPath.getString("public_key");
    String issuer = jsonPath.getString("issuer");
    String kid = jsonPath.getString("kid");
    String alg = jsonPath.getString("alg");
    // client checks
    ClientRepresentation fetchedClient = realm.clients().findByClientId(clientId).get(0);
    assertThat(fetchedClient.isEnabled(), is(true));
    assertThat(fetchedClient.getRedirectUris(), containsInAnyOrder("http://bodhiapp.localhost/app/callback"));
    assertThat(fetchedClient.getWebOrigins(), containsInAnyOrder("+"));
    assertThat(fetchedClient.isConsentRequired(), is(false));
    assertThat(fetchedClient.isStandardFlowEnabled(), is(true));
    assertThat(fetchedClient.isDirectAccessGrantsEnabled(), is(true)); // for test
    assertThat(fetchedClient.isServiceAccountsEnabled(), is(true));
    assertThat(fetchedClient.isPublicClient(), is(false));
    assertThat(fetchedClient.isFullScopeAllowed(), is(false));

    // group checks
    Optional<GroupRepresentation> group = realm.groups().groups().stream()
        .filter(g -> g.getPath().equals(String.format("/users-%s", clientId))).findFirst();
    assertThat(group.isPresent(), is(true));
    assertThat(group.get().getSubGroupCount(), is(4l));
    GroupResource topLevelGroupResource = realm.groups().group(group.get().getId());
    Map<String, Map<String, Set<String>>> groupRoleMap = topLevelGroupResource.getSubGroups(0, -1, false).stream()
        .collect(Collectors.toConcurrentMap(g -> {
          return ((GroupRepresentation) g).getPath();
        }, g -> {
          return g.getClientRoles().keySet().stream()
              .collect(Collectors.toMap(k -> k, v -> new HashSet<>(g.getClientRoles().get(v))));
        }));
    String topLevelGroup = String.format("/users-%s", clientId);
    String usersGroup = String.format("%s/users", topLevelGroup);
    String powerUsersGroup = String.format("%s/power-users", topLevelGroup);
    String managersGroup = String.format("%s/managers", topLevelGroup);
    String adminsGroup = String.format("%s/admins", topLevelGroup);
    Map<String, Map<String, Set<String>>> expected = new HashMap<String, Map<String, Set<String>>>() {
      {
        put(usersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user")));
          }
        });
        put(powerUsersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user", "resource_power_user")));
          }
        });
        put(managersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId,
                new HashSet<String>(Arrays.asList("resource_user", "resource_power_user", "resource_manager")));
          }
        });
        put(adminsGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(
                Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")));
          }
        });
      }
    };
    assertThat(groupRoleMap, equalTo(expected));

    // service account checks
    String resourceToken = getTokenForClient(clientId, clientSecret);
    assertThat(resourceToken, notNullValue());

    // token exchange checks
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
    String exchangeToken = exchangeClientResourceToken(CLIENT_LMNO, userToken, clientId,
        String.format("Bearer %s", resourceToken));
    assertNotNull(exchangeToken);
    AccessToken accessToken = decodeToken(exchangeToken);
    assertThat(clientId, in(accessToken.getAudience()));
    assertEquals("<EMAIL>", accessToken.getPreferredUsername());

    assertThat(publicKey, notNullValue());
    assertThat(issuer, startsWith("http://localhost"));
    assertThat(issuer, endsWith("/realms/bodhi"));
    assertThat(kid, notNullValue());
    assertThat(alg, equalTo("RS256"));
  }
}
