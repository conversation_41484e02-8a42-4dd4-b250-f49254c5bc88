package com.bodhisearch;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;

public class KeycloakScopeTest extends BaseTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(KeycloakScopeTest.class);

  @ParameterizedTest
  @MethodSource("userScopeProvider")
  @SuppressWarnings("unchecked")
  public void testUserScopesAndRefresh(String username, String password, List<String> expectedRoles) {
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");
    String[] tokenPair = getUserTokenPairWith(RESOURCE_ABCD, RESOURCE_SECRET, tokenUrl, username, password, scopes);
    String accessToken = tokenPair[0];
    String refreshToken = tokenPair[1];
    DecodedJWT jwt = JWT.decode(accessToken);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    Claim resourceAccessClaim = jwt.getClaim("resource_access");
    if (expectedRoles.isEmpty()) {
      assertTrue(resourceAccessClaim.isMissing());
      return;
    }
    Map<String, Claim> resourceAccess = (Map<String, Claim>) resourceAccessClaim.asMap().get(RESOURCE_ABCD);
    List<String> resourceRoles = (List<String>) resourceAccess.get("roles");
    assertEquals(expectedRoles.size(), resourceRoles.size());
    assertTrue(resourceRoles.containsAll(expectedRoles));

    String[] newTokenPair = refreshTokenFlow(RESOURCE_ABCD, RESOURCE_SECRET,
        tokenUrl, refreshToken);
    String newAccessToken = newTokenPair[0];
    DecodedJWT newJwt = JWT.decode(newAccessToken);
    Claim newResourceAccessClaim = newJwt.getClaim("resource_access");
    Map<String, Claim> newResourceAccess = (Map<String, Claim>) newResourceAccessClaim.asMap().get(RESOURCE_ABCD);
    List<String> newResourceRoles = (List<String>) newResourceAccess.get("roles");
    assertEquals(expectedRoles.size(), newResourceRoles.size());
    assertTrue(newResourceRoles.containsAll(expectedRoles));
  }

  private static Stream<Arguments> userScopeProvider() {
    return Stream.of(
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList()),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList("resource_user")),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList("resource_user", "resource_power_user")),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList("resource_user", "resource_power_user", "resource_manager")),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList()),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList()),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList()),
        Arguments.of("<EMAIL>", "pass",
            Arrays.asList()));
  }
}
