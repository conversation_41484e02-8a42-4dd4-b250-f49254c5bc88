package com.bodhisearch;

import static com.bodhisearch.AudienceMatchPolicyProvider.verifyTokenWith;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.keycloak.common.VerificationException;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.KeysMetadataRepresentation.KeyMetadataRepresentation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.restassured.response.Response;

public class AudienceMatchPolicyProviderTest extends BaseTest {
  @Test
  public void testValidTokenExchange() {
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass",
        Arrays.asList("openid", "email", "roles"));
    String resource = RESOURCE_ABCD;
    String resourceToken = getTokenForClient(resource, RESOURCE_SECRET);
    Response exchangeResponse = exchangeToken(CLIENT_LMNO, userToken, resource,
        String.format("Bearer %s", resourceToken));
    exchangeResponse.then().body("error_description", nullValue()).statusCode(200);

    String exchangedToken = exchangeResponse.jsonPath().getString("access_token");
    AccessToken token = decodeToken(exchangedToken);

    assertEquals(resource, token.getAudience()[0]);
    assertTrue(token.getResourceAccess().get(resource).getRoles().contains("resource_user"));
  }

  @Test
  public void testResourceMissingTokenExchangePermission() {
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
    String resource = "resource-missing-token-exchange-permission";
    String resourceToken = getTokenForClient(resource, RESOURCE_SECRET);
    exchangeToken(CLIENT_LMNO, userToken, resource,
        String.format("Bearer %s", resourceToken))
        .then()
        .statusCode(403)
        .body("error_description", equalTo("Client not allowed to exchange"));
  }

  private static Stream<Arguments> tokenExchangeArgProvider() {
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
    String resourceToken = getResourceToken();
    return Stream.of(
        Arguments.of("Bearer "),
        Arguments.of(""),
        Arguments.of(String.format("Bearer %s", userToken)),
        Arguments.of(String.format("bearer %s", resourceToken)),
        Arguments.of(userToken),
        Arguments.of((String) null),
        Arguments.of(resourceToken),
        Arguments.of(String.format("Bearer %s", (String) null)));
  }

  @ParameterizedTest
  @MethodSource("tokenExchangeArgProvider")
  public void testTokenExchangeFailsIfAuthorizationHeaderMissing(String authHeader) {
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
    Response exchangeResponse = exchangeToken(CLIENT_LMNO, userToken, "resource-abcd", authHeader);
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied")).body("error_description",
        equalTo("Client not allowed to exchange"));
  }

  @Test
  public void testTokenExchangeFailsIfAudienceDoesNotMatch() {
    String userToken = getUserTokenWith(CLIENT_LMNO, CLIENT_SECRET, tokenUrl, "<EMAIL>", "pass");
    String resourceToken = getOtherResourceToken();
    Response exchangeResponse = exchangeToken(CLIENT_LMNO, userToken, "resource-abcd",
        String.format("Bearer %s", resourceToken));
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied")).body("error_description",
        equalTo("Client not allowed to exchange"));
  }

  @Test
  public void testVerifyTokenWithFailsIfAudienceDoesNotMatch()
      throws JsonMappingException, JsonProcessingException, InvalidKeySpecException, NoSuchAlgorithmException,
      VerificationException {
    String userToken = getResourceToken();
    KeyMetadataRepresentation keyRepr = getKeyRepr(userToken);

    byte[] publicKeyDER = Base64.getDecoder().decode(keyRepr.getPublicKey());
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyDER);
    PublicKey publicKey = keyFactory.generatePublic(keySpec);
    String issuer = String.format("%s/realms/%s", keycloak.getAuthServerUrl(), REALM);
    verifyTokenWith(userToken, issuer, "resource-abcd", publicKey);
  }
}
