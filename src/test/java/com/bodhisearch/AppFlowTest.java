package com.bodhisearch;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.hamcrest.Matchers;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;

public class AppFlowTest extends BaseTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(AppFlowTest.class);

  @Test
  public void testMakeResourceAdmin() {
    String[] tokenPair = registerClientAndReturnTokenPair();
    String clientId = tokenPair[0];
    String clientSecret = tokenPair[1];
    String clientToken = getTokenForClient(clientId, clientSecret);

    makeResourceAdmin(resourceAdminUrl, clientToken, "<EMAIL>")
        .then()
        .statusCode(201);
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");
    String[] userTokenPair = getUserTokenPairWith(clientId, clientSecret, tokenUrl, "<EMAIL>", "pass",
        scopes);
    String userAccessToken = userTokenPair[0];
    DecodedJWT jwt = JWT.decode(userAccessToken);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));
    assertEquals("RS256", jwt.getAlgorithm());
    String keyId = jwt.getKeyId();

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientId))
        .get("roles");
    List<String> allRoles = Arrays.asList(ROLE_RESOURCE_USER, ROLE_RESOURCE_POWER_USER, ROLE_RESOURCE_MANAGER,
        ROLE_RESOURCE_ADMIN);
    assertThat(allRoles, Matchers.containsInAnyOrder(resourceRoles.toArray()));

    String[] newTokenPair = refreshTokenFlow(clientId, clientSecret, tokenUrl, userTokenPair[1]);
    String newAccessToken = newTokenPair[0];
    DecodedJWT newJwt = JWT.decode(newAccessToken);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientId))
        .get("roles");
    assertThat(allRoles, Matchers.containsInAnyOrder(newResourceRoles.toArray()));
  }

  @Test
  public void testGrantUserRoleUser() {
    String[] tokenPair = registerClientAndReturnTokenPair();
    String clientId = tokenPair[0];
    String clientSecret = tokenPair[1];
    String clientToken = getTokenForClient(clientId, clientSecret);

    makeResourceAdmin(resourceAdminUrl, clientToken, "<EMAIL>")
        .then()
        .statusCode(201);
    String adminAccessToken = getUserTokenWith(clientId, clientSecret, tokenUrl, "<EMAIL>", "pass");

    addUserToGroup(addToGroupUrl, adminAccessToken, "<EMAIL>", "users").then()
        .body("error", nullValue())
        .statusCode(201);

    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");
    String[] userTokenPair = getUserTokenPairWith(
        clientId,
        clientSecret,
        tokenUrl,
        "<EMAIL>",
        "pass",
        scopes);
    String userAccessToken = userTokenPair[0];
    DecodedJWT jwt = JWT.decode(userAccessToken);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientId))
        .get("roles");
    List<String> userRoles = Arrays.asList(ROLE_RESOURCE_USER);
    assertThat(userRoles, Matchers.containsInAnyOrder(resourceRoles.toArray()));
    String keyId = jwt.getKeyId();
    assertEquals("RS256", jwt.getAlgorithm());

    String[] newTokenPair = refreshTokenFlow(clientId, clientSecret, tokenUrl, userTokenPair[1]);
    String newAccessToken = newTokenPair[0];
    DecodedJWT newJwt = JWT.decode(newAccessToken);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientId))
        .get("roles");
    assertThat(userRoles, Matchers.containsInAnyOrder(newResourceRoles.toArray()));
  }

  @Test
  public void testOfflineTokenForAdminWithTokenScopePowerUserLevel() {
    String[] tokenPair = registerClientAndReturnTokenPair();
    String clientId = tokenPair[0];
    String clientSecret = tokenPair[1];
    String clientToken = getTokenForClient(clientId, clientSecret);

    makeResourceAdmin(resourceAdminUrl, clientToken, "<EMAIL>")
        .then()
        .statusCode(201);

    String[] userTokenPair = getUserTokenPairWith(clientId, clientSecret, tokenUrl, "<EMAIL>", "pass",
        Arrays.asList("openid", "email", "profile", "roles"));

    String userAccessToken = userTokenPair[0];
    DecodedJWT jwt = JWT.decode(userAccessToken);
    String keyId = jwt.getKeyId();
    String[] offlineTokenPair = exchangeToOfflineToken(clientId, clientSecret, userAccessToken,
        Arrays.asList("offline_access", "scope_token_power_user"));
    String offlineAccessToken = offlineTokenPair[0];
    String offlineRefreshToken = offlineTokenPair[1];

    DecodedJWT offlineJwt = JWT.decode(offlineAccessToken);
    String offlineClaimScope = offlineJwt.getClaim("scope").asString();
    List<String> offlineClaimScopes = Arrays.asList(offlineClaimScope.split("\\s+"));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(offlineClaimScopes));
    assertTrue(offlineJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, offlineJwt.getKeyId());
    assertEquals("RS256", offlineJwt.getAlgorithm());

    String[] newOfflineTokens = refreshTokenFlow(clientId, clientSecret, tokenUrl, offlineRefreshToken);
    DecodedJWT newOfflineJwt = JWT.decode(newOfflineTokens[0]);
    String newOfflineClaimScope = newOfflineJwt.getClaim("scope").asString();
    List<String> newOfflineClaimScopes = Arrays.asList(newOfflineClaimScope.split("\\s+"));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(newOfflineClaimScopes));
    assertTrue(newOfflineJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, newOfflineJwt.getKeyId());
    assertEquals("RS256", newOfflineJwt.getAlgorithm());
  }
}
