FROM openjdk:21-jdk AS builder
WORKDIR /app
COPY . .
RUN ./mvnw clean package -DskipTests

FROM quay.io/keycloak/keycloak:26.2.5 AS optimizer

COPY --from=builder /app/target/keycloak-bodhi-ext.jar /opt/keycloak/providers/keycloak-bodhi-ext.jar
COPY cache-ispn-jdbc-ping.xml /opt/keycloak/conf/cache-ispn-jdbc-ping.xml

RUN /opt/keycloak/bin/kc.sh build \
    --db=postgres \
    --cache=ispn \
    # --cache-config-file=cache-ispn-jdbc-ping.xml \
    # --features=admin-fine-grained-authz,token-exchange,persistent-user-sessions \ # available in > v25.0
    --features=admin-fine-grained-authz,token-exchange \
    --http-relative-path=/ \
    --health-enabled=true

FROM quay.io/keycloak/keycloak:26.2.5

COPY --from=optimizer /opt/keycloak/ /opt/keycloak/
ENTRYPOINT ["/opt/keycloak/bin/kc.sh"]
