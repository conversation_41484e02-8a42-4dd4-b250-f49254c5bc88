version: '3'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password

  keycloak:
    build: .
    ports:
      - "8080:8080"
    command:
      - start
    environment:
      KC_DB: postgres
      KC_DB_URL: ***********************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KC_HEALTH_ENABLED: true
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_PROXY: edge
      KC_LOG_LEVEL: INFO
      KC_CACHE: ispn
      KC_DB_POOL_INITIAL_SIZE: 5
      KC_DB_POOL_MIN_SIZE: 5
      KC_DB_POOL_MAX_SIZE: 15
      KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_FAILURES: 5
      KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_WAIT_INCREMENT: 60
      KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_WAIT: 300
      KC_FEATURES: admin-fine-grained-authz,token-exchange
      JAVA_OPTS: -Xms512m -Xmx1g -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m
      QUARKUS_TRANSACTION_MANAGER_ENABLE_RECOVERY: true
      KC_PROXY_HEADERS: forwarded
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin

    depends_on:
      - postgres
